# Rangy Highlight Replacement Implementation

This project implements a sophisticated text highlighting system using the Rangy library that supports **selective highlight replacement** without overlapping. This means when you highlight a portion of already highlighted text with a different color, it replaces (not overlaps) the existing highlight in that portion while preserving the rest.

## 🎯 Key Features

### ✅ Selective Highlight Replacement
- **Scenario**: Text "it is the study of bodies in motion" is highlighted in yellow
- **Action**: User selects just "study" and highlights it in green  
- **Result**: "it is the" (yellow) + "study" (green) + "of bodies in motion" (yellow)
- **Behavior**: The original yellow highlight is split, and the overlapping portion is replaced with green

### ✅ Advanced Functionality
- **Multiple Colors**: Support for 5 different highlight colors (Yellow, Green, Blue, Pink, Purple)
- **Range Splitting**: Automatically splits existing highlights when new highlights overlap
- **Preservation**: Non-overlapping portions of original highlights are preserved
- **Click Handlers**: All highlights are clickable with proper event handling
- **Serialization**: Proper storage and retrieval of highlight data using <PERSON>ng<PERSON>'s serialization

## 🏗️ Architecture

### Core Components

1. **RangyHighlighterWrapper** (`src/utils/rangyHighlightUtils.js`)
   - Main wrapper class around <PERSON><PERSON><PERSON>'s highlighter
   - Implements replacement logic
   - Handles overlap detection and range splitting

2. **HighlightReplacementDemo** (`src/components/HighlightReplacementDemo.jsx`)
   - React component demonstrating the functionality
   - Interactive UI with color selection
   - Real-time logging of operations

3. **Standalone Test** (`test-highlight-replacement.html`)
   - Pure HTML/JavaScript implementation
   - No React dependencies
   - Direct Rangy API usage

### Key Methods

#### `replaceHighlightSelection(color)`
Main method that orchestrates the replacement process:
1. Finds overlapping highlights
2. Removes overlapping highlights and calculates preserved ranges
3. Applies new highlight to selection
4. Restores non-overlapping parts of removed highlights

#### `findOverlappingHighlights(range)`
- Detects all highlight elements that overlap with the new selection
- Uses DOM range comparison methods
- Returns array of overlapping highlight data

#### `removeOverlappingHighlights(overlappingHighlights, newRange)`
- Calculates before/after ranges for each overlapping highlight
- Removes original highlight elements from DOM
- Returns preserved ranges with their original colors

#### `restorePreservedRanges(preservedRanges)`
- Re-applies highlights to non-overlapping portions
- Maintains original colors for preserved text
- Uses Rangy's highlighter for consistent styling

## 🚀 Usage

### Running the Demo

1. **React Component Demo**:
   ```bash
   npm run dev
   ```
   - Open http://localhost:5174/
   - Click "🎯 Highlight Demo" button
   - Follow the test instructions

2. **Standalone HTML Demo**:
   - Open `test-highlight-replacement.html` in browser
   - No build process required
   - Direct Rangy library usage

### Test Scenario

1. Select "it is the study of bodies in motion" → Highlight with **Yellow**
2. Select just "study" → Highlight with **Green**
3. Result: "it is the" (yellow) + "study" (green) + "of bodies in motion" (yellow)

### API Usage

```javascript
// Initialize highlighter
const highlighter = await createHighlighter(
  containerElement,
  onHighlightClick,
  onSelectionCreate,
  getCurrentColor
);

// Start highlighting
highlighter.run();

// The replacement happens automatically when user selects text
// No additional API calls needed
```

## 🔧 Technical Implementation

### Rangy Configuration

```javascript
// Class appliers for each color
const applier = rangy.createClassApplier(className, {
  elementTagName: "span",
  normalize: true,
  useExistingElements: false // Important for replacement functionality
});
```

### Range Overlap Detection

```javascript
function rangesOverlap(range1, range2) {
  const comparison1 = range1.compareBoundaryPoints(Range.START_TO_END, range2);
  const comparison2 = range1.compareBoundaryPoints(Range.END_TO_START, range2);
  return comparison1 > 0 && comparison2 < 0;
}
```

### Highlight Splitting Logic

```javascript
// Calculate before range
const beforeRange = document.createRange();
beforeRange.setStart(originalRange.startContainer, originalRange.startOffset);
beforeRange.setEnd(newRange.startContainer, newRange.startOffset);

// Calculate after range  
const afterRange = document.createRange();
afterRange.setStart(newRange.endContainer, newRange.endOffset);
afterRange.setEnd(originalRange.endContainer, originalRange.endOffset);
```

## 📁 File Structure

```
src/
├── utils/
│   └── rangyHighlightUtils.js     # Core highlighting logic
├── components/
│   └── HighlightReplacementDemo.jsx  # React demo component
├── App.jsx                        # Main app with demo toggle
└── App.css                        # Highlight styles

test-highlight-replacement.html    # Standalone demo
README-HIGHLIGHT-REPLACEMENT.md   # This documentation
```

## 🎨 Styling

Highlights use CSS classes with the pattern `rangy-highlight-{color}`:

```css
.rangy-highlight-yellow {
  background-color: rgba(255, 235, 59, 0.3) !important;
  border-bottom: 2px solid #ffeb3b !important;
  cursor: pointer !important;
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Highlights not appearing**: Check if Rangy is properly initialized
2. **Replacement not working**: Verify `useExistingElements: false` in class applier config
3. **Range errors**: Ensure selections are within the container element
4. **Click handlers not working**: Check if `data-highlight-id` attributes are set

### Debug Logging

The implementation includes comprehensive logging:
- `🎯` Starting replacement highlight
- `🔍` Found overlapping highlights  
- `🗑️` Removed overlapping highlights
- `✅` Applied/restored highlights

## 📚 Dependencies

- **rangy**: ^1.3.2 - Core highlighting library
- **react**: ^19.1.0 - UI framework (for React demo)
- **zustand**: ^5.0.5 - State management (existing project dependency)

## 🔮 Future Enhancements

- [ ] Undo/Redo functionality
- [ ] Highlight annotations/notes
- [ ] Export/import highlight data
- [ ] Collaborative highlighting
- [ ] Performance optimization for large documents
- [ ] Mobile touch support improvements

## 📄 License

This implementation follows the same license as the main project.
