import { useState, useEffect, useRef } from "react";
import "./App.css";
import { useAudioStore } from "./stores/audioStore";
import CourseStructure from "./components/CourseStructure";
import MainContent from "./components/MainContent";
import ToolsPanel from "./components/ToolsPanel";
import ErrorBoundary from "./components/ErrorBoundary";

import { ToastContainer } from "./components/Toast";
import { useErrorHandler } from "./hooks/useErrorHandler";
import { useLocalStorage } from "./hooks/useLocalStorage";
import { STORAGE_KEYS, TEXT_SIZES } from "./utils/constants";

const mockCourse = {
  id: "1",
  title: "Physics - Mechanics",
  topics: [
    {
      id: "dynamics",
      title: "Dynamics",
      currentPage: 1,
      expanded: true,
      subTopics: [
        {
          id: "rectilinear-motion",
          title: "Rectilinear motion",
          currentPage: 1,
          pages: [
            {
              id: "page-1",
              pageNumber: 1,
              title: "Kinematics of Particle",
              audioUrls: ["/audio1.mp3", "/audio4.mp3", "/audio2.mp3"],
              contents: [
                {
                  id: "content-1",
                  type: "html",
                  order: 1,
                  content: `<h3>Test Kinematics of Particle</h3>
                                    <p>It is the study of bodies in motion, it is into divided into two parts</p>
                                    <p><strong>1. Kinematics</strong> (study of geometry of motion) -</p>
                                    <p>It is the study of relation between displacement (s), velocity (v), acceleration (a) and time (t).</p>
                                    <p><strong>2. Kinetics</strong> -</p>
                                    <p>It is the study of relation between force (f), mass (m), displacement (s), velocity (v), acceleration (a) and time (t).</p>

                                    <p><strong>Types of motion based on geometry</strong> -</p>
                                    <p><strong>1. Translation</strong> -</p>
                                    <p>During the motion of translation, orientation of a body does not change. Translation is of two types :</p>
                                    <ul>
                                        <li><strong>Rectilinear translation.</strong></li>
                                        <li><strong>Curvilinear translation.</strong></li>
                                    </ul>

                                    <div class="diagram-container" style="display: flex; gap: 64px; margin: 40px 0;">
                                        <img src="/image1.png" alt="Physics Diagram 1 - Rectilinear Translation Illustration" class="diagram-image">
                                        <img src="/image2.png" alt="Physics Diagram 2 - Curvilinear Translation Illustration" class="diagram-image">
                                    </div>

                                    <p><strong>2. Rotation</strong> -</p>
                                    <p>During the motion of rotation, <span>all the particles will move along concentric circles</span>.</p>`,
                },
              ],
            },
            {
              id: "page-2",
              pageNumber: 2,
              title: "Types of Motion",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-2",
                  type: "text",
                  order: 1,
                  content: `<h3>Types of Motion</h3>
                                    <p>Motion can be classified in various ways based on different criteria:</p>

                                    <p><strong>Based on Path:</strong></p>
                                    <ul>
                                        <li><strong>Linear Motion:</strong> Motion along a straight line</li>
                                        <li><strong>Circular Motion:</strong> Motion along a circular path</li>
                                        <li><strong>Random Motion:</strong> Motion with no fixed pattern</li>
                                    </ul>

                                    <p><strong>Based on Nature:</strong></p>
                                    <ul>
                                        <li><strong>Uniform Motion:</strong> Motion with constant velocity</li>
                                        <li><strong>Non-uniform Motion:</strong> Motion with changing velocity</li>
                                    </ul>

                                    <p>Understanding these classifications helps in analyzing different types of mechanical systems and their behavior.</p>`,
                },
              ],
            },
            {
              id: "page-3",
              pageNumber: 3,
              title: "Equations of Motion",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-3",
                  type: "text",
                  order: 1,
                  content: `<h3>Equations of Motion</h3>
                                    <p>For uniformly accelerated motion, we have three fundamental equations:</p>

                                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
                                        <p><strong>First Equation:</strong> v = u + at</p>
                                        <p><strong>Second Equation:</strong> s = ut + ½at²</p>
                                        <p><strong>Third Equation:</strong> v² = u² + 2as</p>
                                    </div>

                                    <p>Where:</p>
                                    <ul>
                                        <li>u = initial velocity</li>
                                        <li>v = final velocity</li>
                                        <li>a = acceleration</li>
                                        <li>t = time</li>
                                        <li>s = displacement</li>
                                    </ul>`,
                },
              ],
            },
            {
              id: "page-4",
              pageNumber: 4,
              title: "Applications",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-4",
                  type: "text",
                  order: 1,
                  content: `<h3>Applications of Rectilinear Motion</h3>
                                    <p>Rectilinear motion principles are applied in various engineering fields:</p>

                                    <p><strong>Automotive Engineering:</strong></p>
                                    <ul>
                                        <li>Vehicle acceleration and braking systems</li>
                                        <li>Crash test analysis</li>
                                        <li>Engine piston motion</li>
                                    </ul>

                                    <p><strong>Aerospace Engineering:</strong></p>
                                    <ul>
                                        <li>Rocket launch trajectories</li>
                                        <li>Aircraft takeoff and landing</li>
                                        <li>Satellite orbital mechanics</li>
                                    </ul>

                                    <p><strong>Civil Engineering:</strong></p>
                                    <ul>
                                        <li>Elevator design</li>
                                        <li>Bridge dynamics</li>
                                        <li>Structural vibration analysis</li>
                                    </ul>`,
                },
              ],
            },
          ],
        },
        {
          id: "projectile-motion",
          title: "Projectile motion",
          currentPage: 1,
          pages: [
            {
              id: "page-5",
              pageNumber: 1,
              title: "Projectile Motion Basics",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-5",
                  type: "text",
                  order: 1,
                  content: `<h3>Projectile Motion</h3>
                                    <p>Projectile motion is the motion of an object thrown or projected into the air, subject only to acceleration due to gravity.</p>

                                    <p><strong>Key Characteristics:</strong></p>
                                    <ul>
                                        <li>Two-dimensional motion</li>
                                        <li>Constant horizontal velocity</li>
                                        <li>Uniformly accelerated vertical motion</li>
                                        <li>Parabolic trajectory</li>
                                    </ul>

                                    <p><strong>Components of Projectile Motion:</strong></p>
                                    <p><strong>Horizontal Component:</strong> vₓ = v₀ cos θ</p>
                                    <p><strong>Vertical Component:</strong> vᵧ = v₀ sin θ - gt</p>

                                    <p>Where v₀ is initial velocity, θ is launch angle, and g is acceleration due to gravity.</p>`,
                },
              ],
            },
            {
              id: "page-6",
              pageNumber: 2,
              title: "Trajectory Analysis",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-6",
                  type: "text",
                  order: 1,
                  content: `<h3>Trajectory Analysis</h3>
                                    <p>The path followed by a projectile is called its trajectory, which is always parabolic in nature.</p>

                                    <p><strong>Important Parameters:</strong></p>
                                    <ul>
                                        <li><strong>Range (R):</strong> R = (v₀² sin 2θ)/g</li>
                                        <li><strong>Maximum Height (H):</strong> H = (v₀² sin² θ)/(2g)</li>
                                        <li><strong>Time of Flight (T):</strong> T = (2v₀ sin θ)/g</li>
                                    </ul>

                                    <p><strong>Optimal Launch Angle:</strong></p>
                                    <p>For maximum range on level ground, the optimal launch angle is 45°.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "tangential-normal",
          title: "Tangential and normal components",
          currentPage: 1,
          pages: [
            {
              id: "page-7",
              pageNumber: 1,
              title: "Tangential and Normal Components",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-7",
                  type: "text",
                  order: 1,
                  content: `<h3>Tangential and Normal Components</h3>
                                    <p>In curvilinear motion, acceleration can be resolved into two components:</p>

                                    <p><strong>Tangential Component (aₜ):</strong></p>
                                    <ul>
                                        <li>Acts along the tangent to the path</li>
                                        <li>Responsible for change in speed</li>
                                        <li>aₜ = dv/dt</li>
                                    </ul>

                                    <p><strong>Normal Component (aₙ):</strong></p>
                                    <ul>
                                        <li>Acts perpendicular to the path</li>
                                        <li>Responsible for change in direction</li>
                                        <li>aₙ = v²/ρ (where ρ is radius of curvature)</li>
                                    </ul>

                                    <p>Total acceleration: a = √(aₜ² + aₙ²)</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "motion-fill",
          title: "Motion in polar co-ordinates ",
          currentPage: 1,
          pages: [
            {
              id: "page-8",
              pageNumber: 1,
              title: "Motion in polar co-ordinates",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-8",
                  type: "text",
                  order: 1,
                  content: `<h3>Motion Fill</h3>
                                    <p>Advanced motion analysis techniques and applications.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "kinetics-rectilinear",
          title: "Kinetics of rectilinear motion",
          currentPage: 1,
          pages: [
            {
              id: "page-9",
              pageNumber: 1,
              title: "Kinetics of Rectilinear Motion",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-9",
                  type: "text",
                  order: 1,
                  content: `<h3>Kinetics of Rectilinear Motion</h3>
                                    <p>Study of forces and their effects on rectilinear motion.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "kinetics-curvilinear",
          title: "Kinetics of curvilinear Motion",
          currentPage: 1,
          pages: [
            {
              id: "page-10",
              pageNumber: 1,
              title: "Kinetics of Curvilinear Motion",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-10",
                  type: "text",
                  order: 1,
                  content: `<h3>Kinetics of Curvilinear Motion</h3>
                                    <p>Analysis of forces in curvilinear motion systems.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "work-energy",
          title: "Work energy",
          currentPage: 1,
          pages: [
            {
              id: "page-11",
              pageNumber: 1,
              title: "Work Energy",
              audioUrl: "/audio2.mp3",
              contents: [
                {
                  id: "content-11",
                  type: "text",
                  order: 1,
                  content: `<h3>Work Energy</h3>
                                    <p>Work-energy theorem and its applications in mechanics.</p>`,
                },
              ],
            },
          ],
        },
        {
          id: "impulse-momentum",
          title: "Impulse momentum and impact",
          currentPage: 1,
          pages: [
            {
              id: "page-12",
              pageNumber: 1,
              title: "Impulse Momentum and Impact",
              audioUrl: "/audio3.mp3",
              contents: [
                {
                  id: "content-12",
                  type: "text",
                  order: 1,
                  content: `<h3>Impulse Momentum and Impact</h3>
                                    <p>Conservation of momentum and impact analysis.</p>`,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: "statics",
      title: "Statics",
      currentPage: 1,
      expanded: true,
      subTopics: [
        {
          id: "resultant-force-system",
          title: "Resultant of force system",
          currentPage: 1,
          pages: [
            {
              id: "page-13",
              pageNumber: 1,
              title: "Resultant of Force System",
              audioUrl: "/audio1.mp3",
              contents: [
                {
                  id: "content-13",
                  type: "text",
                  order: 1,
                  content: `<h3>Resultant of Force System</h3>
                                    <p>The resultant of a force system is a single force that produces the same effect as the combined action of all forces in the system.</p>

                                    <p><strong>Types of Force Systems:</strong></p>
                                    <ul>
                                        <li><strong>Concurrent Forces:</strong> Forces that meet at a common point</li>
                                        <li><strong>Parallel Forces:</strong> Forces that are parallel to each other</li>
                                        <li><strong>Non-concurrent Forces:</strong> Forces that do not meet at a common point</li>
                                    </ul>

                                    <p><strong>Methods of Finding Resultant:</strong></p>
                                    <ul>
                                        <li>Graphical method (Parallelogram law, Triangle law)</li>
                                        <li>Analytical method (Component method)</li>
                                    </ul>`,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};

// Helper function to convert new course structure to flat array for compatibility
// Helper function to remove heading tags from HTML content
const stripHeadingTags = (htmlContent) => {
  // Remove h1-h6 tags and their content entirely to avoid duplication with the main heading
  return htmlContent.replace(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi, "").trim();
};

const convertCourseToFlatStructure = (course) => {
  const flatTopics = [];

  course.topics.forEach((topic) => {
    topic.subTopics.forEach((subTopic) => {
      // Convert all pages for this subtopic
      const pages = subTopic.pages.map((page) => {
        const content = page.contents[0]; // Taking first content for now
        const htmlContent = stripHeadingTags(content.content);

        // Handle both single audio file (audioUrl) and multiple audio files (audioUrls)
        const audioSources =
          page.audioUrls || (page.audioUrl ? [page.audioUrl] : []);

        return {
          id: page.id,
          pageNumber: page.pageNumber,
          title: page.title,
          audioSources: audioSources,
          content: {
            heading: page.title,
            body: [
              {
                id: `content-${page.id}`,
                type: "html",
                content: htmlContent,
              },
            ],
          },
        };
      });

      // Create one entry per subtopic (not per page)
      flatTopics.push({
        id: subTopic.id,
        title: subTopic.title,
        currentPageIndex: 0, // Start with first page
        pages: pages,
        // Keep reference to original structure for navigation
        originalStructure: {
          topicId: topic.id,
          subTopicId: subTopic.id,
          totalPages: subTopic.pages.length,
        },
      });
    });
  });

  return flatTopics;
};

const initialCourseData = convertCourseToFlatStructure(mockCourse);

function App() {
  // Enhanced state management with localStorage persistence
  const [courseTopics, setCourseTopics] = useState(initialCourseData);
  const [activeTopicId, setActiveTopicId] = useState(courseTopics[0]?.id);
  const [textSize, setTextSize] = useLocalStorage(
    STORAGE_KEYS.TEXT_SIZE,
    TEXT_SIZES.NORMAL
  );

  const audioRef = useRef(null);

  // Error handling
  const { errors, addError, removeError } = useErrorHandler();

  const initialize = useAudioStore((state) => state.initialize);
  const loadPagePlaylist = useAudioStore((state) => state.loadPagePlaylist);
  const currentPageId = useAudioStore((state) => state.currentPageId);

  // Handle page navigation within a subtopic
  const handlePageNavigation = (topicId, pageNumber) => {
    setCourseTopics((prevTopics) =>
      prevTopics.map((topic) => {
        if (topic.id === topicId) {
          const newPageIndex = pageNumber - 1; // Convert to 0-based index
          if (newPageIndex >= 0 && newPageIndex < topic.pages.length) {
            return { ...topic, currentPageIndex: newPageIndex };
          }
        }
        return topic;
      })
    );
  };

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  // Initialize audio store
  useEffect(() => {
    if (audioRef.current) {
      const cleanup = initialize(audioRef, []);
      return cleanup;
    }
  }, [initialize]);

  // Load page playlist when active topic or page changes
  useEffect(() => {
    if (courseTopics.length > 0) {
      const activeTopic = courseTopics.find((t) => t.id === activeTopicId);
      if (activeTopic && activeTopic.pages && activeTopic.pages.length > 0) {
        const currentPage =
          activeTopic.pages[activeTopic.currentPageIndex || 0];
        const pageId = `${activeTopic.id}-page-${currentPage.pageNumber}`;

        if (pageId !== currentPageId) {
          // Load the playlist for the current page
          loadPagePlaylist(pageId, currentPage.audioSources);
        }
      }
    }
  }, [activeTopicId, courseTopics, loadPagePlaylist, currentPageId]);

  const activeTopic = courseTopics.find((topic) => topic.id === activeTopicId);

  // Navigation function for bookmarks
  const handleBookmarkNavigation = useCallback(
    (topicId, pageNumber) => {
      console.log(`📍 Navigating to topic: ${topicId}, page: ${pageNumber}`);

      // First, set the active topic
      setActiveTopicId(topicId);

      // Then navigate to the specific page
      handlePageNavigation(topicId, pageNumber);
    },
    [handlePageNavigation]
  );

  // Get current page data for MainContent
  const getCurrentPageData = () => {
    if (!activeTopic || !activeTopic.pages || activeTopic.pages.length === 0) {
      return null;
    }

    const currentPage = activeTopic.pages[activeTopic.currentPageIndex || 0];
    const pageData = {
      id: `${activeTopic.id}-page-${currentPage.pageNumber}`,
      title: `${activeTopic.title} - ${currentPage.title}`,
      audioSources: currentPage.audioSources,
      content: currentPage.content,
      // Add the real topic ID for highlighting system
      realTopicId: activeTopic.id,
      originalStructure: {
        ...activeTopic.originalStructure,
        pageId: currentPage.id,
        pageNumber: currentPage.pageNumber,
      },
    };

    console.log("📄 getCurrentPageData returning:", pageData);
    console.log("📄 Current page content structure:", currentPage.content);

    return pageData;
  };

  const currentPageData = getCurrentPageData();

  return (
    <ErrorBoundary
      message="Something went wrong with the application. Please refresh the page."
      onError={(error, errorInfo) => {
        console.error("App Error Boundary:", error, errorInfo);
        addError("Application error occurred");
      }}
    >
      <div className="app-container">
        <audio
          ref={audioRef}
          style={{ display: "none" }}
          preload="auto"
          crossOrigin="anonymous"
        />

        <ErrorBoundary message="Course navigation error occurred">
          <CourseStructure
            topics={courseTopics}
            activeTopicId={activeTopicId}
            onSelectTopic={setActiveTopicId}
            courseData={mockCourse}
            onNavigateToPage={handleBookmarkNavigation}
          />
        </ErrorBoundary>

        <ErrorBoundary message="Content display error occurred">
          <MainContent
            topic={
              currentPageData || (courseTopics[0] ? getCurrentPageData() : null)
            }
            textSize={textSize}
          />
        </ErrorBoundary>

        <ErrorBoundary message="Tools panel error occurred">
          <ToolsPanel
            activeTextSize={textSize}
            onSetTextSize={setTextSize}
            currentTopic={activeTopic}
            onPageChange={handlePageNavigation}
          />
        </ErrorBoundary>

        {/* Toast notifications for errors */}
        <ToastContainer
          toasts={errors.map((error) => ({
            id: error.id,
            message: error.message,
            type: "error",
            duration: 5000,
            showCloseButton: true,
          }))}
          onRemoveToast={removeError}
        />
      </div>
    </ErrorBoundary>
  );
}

export default App;
