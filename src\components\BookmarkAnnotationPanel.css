.bookmark-annotation-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.bookmark-annotation-panel > div {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 800px;
  height: 80%;
  max-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.panel-header h2 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background-color: #e0e0e0;
  color: #333;
}

.panel-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.tab-btn {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  transition: all 0.2s;
  border-bottom: 3px solid transparent;
}

.tab-btn:hover {
  background-color: #e9ecef;
  color: #333;
}

.tab-btn.active {
  color: #007bff;
  border-bottom-color: #007bff;
  background-color: white;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.empty-state p:first-child {
  font-size: 18px;
  margin-bottom: 8px;
}

.empty-state p:last-child {
  font-size: 14px;
  color: #999;
}

/* Bookmarks */
.bookmarks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.bookmark-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.2s;
}

.bookmark-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
}

.bookmark-content {
  flex: 1;
}

.bookmark-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.bookmark-meta {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.bookmark-actions {
  display: flex;
  gap: 8px;
}

/* Annotations */
.add-annotation {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
}

.add-annotation h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #333;
}

.annotation-title-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  margin-bottom: 12px;
  box-sizing: border-box;
}

.annotation-content-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  margin-bottom: 12px;
  box-sizing: border-box;
}

.annotation-form-actions {
  display: flex;
  gap: 8px;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-primary:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #545b62;
}

.annotations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.annotation-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: all 0.2s;
}

.annotation-item:hover {
  border-color: #28a745;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.1);
}

.annotation-content {
  flex: 1;
  margin-right: 12px;
}

.annotation-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
  font-weight: 600;
}

.annotation-text {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #555;
  line-height: 1.4;
  white-space: pre-wrap;
}

.annotation-meta {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.annotation-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* Action Buttons */
.action-btn {
  background: none;
  border: 1px solid #ddd;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background-color: #f0f0f0;
}

.navigate-btn:hover {
  background-color: #e3f2fd;
  border-color: #2196f3;
}

.edit-btn:hover {
  background-color: #fff3e0;
  border-color: #ff9800;
}

.delete-btn:hover {
  background-color: #ffebee;
  border-color: #f44336;
}

/* Responsive */
@media (max-width: 768px) {
  .bookmark-annotation-panel > div {
    width: 95%;
    height: 90%;
  }
  
  .panel-header {
    padding: 16px 20px;
  }
  
  .panel-content {
    padding: 16px 20px;
  }
  
  .bookmark-item,
  .annotation-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bookmark-actions,
  .annotation-actions {
    margin-top: 12px;
    justify-content: flex-end;
  }
}
