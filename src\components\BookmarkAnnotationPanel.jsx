import { useState, useCallback } from "react";
import { useBookmarkStore } from "../stores/bookmarkStore";
import "./BookmarkAnnotationPanel.css";

const BookmarkAnnotationPanel = ({ onNavigateToPage, onClose }) => {
  const [activeTab, setActiveTab] = useState("bookmarks");
  const [newAnnotation, setNewAnnotation] = useState({ title: "", content: "" });
  const [editingAnnotation, setEditingAnnotation] = useState(null);

  // Store hooks
  const bookmarks = useBookmarkStore((state) => state.bookmarks);
  const annotations = useBookmarkStore((state) => state.annotations);
  const removeBookmark = useBookmarkStore((state) => state.removeBookmark);
  const addAnnotation = useBookmarkStore((state) => state.addAnnotation);
  const updateAnnotation = useBookmarkStore((state) => state.updateAnnotation);
  const removeAnnotation = useBookmarkStore((state) => state.removeAnnotation);

  const handleNavigateToBookmark = useCallback(
    (bookmark) => {
      if (onNavigateToPage) {
        onNavigateToPage(bookmark.topicId, bookmark.pageNumber);
      }
    },
    [onNavigateToPage]
  );

  const handleNavigateToAnnotation = useCallback(
    (annotation) => {
      if (onNavigateToPage) {
        onNavigateToPage(annotation.topicId, annotation.pageNumber);
      }
    },
    [onNavigateToPage]
  );

  const handleAddAnnotation = useCallback(() => {
    if (!newAnnotation.title.trim()) return;

    addAnnotation({
      title: newAnnotation.title,
      content: newAnnotation.content,
      topicId: "general", // Will be updated when we have current context
      pageId: "general",
      pageNumber: 1,
    });

    setNewAnnotation({ title: "", content: "" });
  }, [newAnnotation, addAnnotation]);

  const handleEditAnnotation = useCallback(
    (annotation) => {
      setEditingAnnotation(annotation);
      setNewAnnotation({
        title: annotation.title,
        content: annotation.content,
      });
    },
    []
  );

  const handleUpdateAnnotation = useCallback(() => {
    if (!editingAnnotation || !newAnnotation.title.trim()) return;

    updateAnnotation(editingAnnotation.id, {
      title: newAnnotation.title,
      content: newAnnotation.content,
    });

    setEditingAnnotation(null);
    setNewAnnotation({ title: "", content: "" });
  }, [editingAnnotation, newAnnotation, updateAnnotation]);

  const handleCancelEdit = useCallback(() => {
    setEditingAnnotation(null);
    setNewAnnotation({ title: "", content: "" });
  }, []);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="bookmark-annotation-panel">
      <div className="panel-header">
        <h2>📚 Bookmarks & Annotations</h2>
        <button className="close-btn" onClick={onClose} title="Close">
          ✕
        </button>
      </div>

      <div className="panel-tabs">
        <button
          className={`tab-btn ${activeTab === "bookmarks" ? "active" : ""}`}
          onClick={() => setActiveTab("bookmarks")}
        >
          🔖 Bookmarks ({bookmarks.length})
        </button>
        <button
          className={`tab-btn ${activeTab === "annotations" ? "active" : ""}`}
          onClick={() => setActiveTab("annotations")}
        >
          📝 Annotations ({annotations.length})
        </button>
      </div>

      <div className="panel-content">
        {activeTab === "bookmarks" && (
          <div className="bookmarks-section">
            {bookmarks.length === 0 ? (
              <div className="empty-state">
                <p>📖 No bookmarks yet</p>
                <p>Use the bookmark button in the top-right corner to save pages!</p>
              </div>
            ) : (
              <div className="bookmarks-list">
                {bookmarks.map((bookmark) => (
                  <div key={bookmark.id} className="bookmark-item">
                    <div className="bookmark-content">
                      <h4 className="bookmark-title">{bookmark.title}</h4>
                      <p className="bookmark-meta">
                        Page {bookmark.pageNumber} • {formatDate(bookmark.createdAt)}
                      </p>
                    </div>
                    <div className="bookmark-actions">
                      <button
                        className="action-btn navigate-btn"
                        onClick={() => handleNavigateToBookmark(bookmark)}
                        title="Go to page"
                      >
                        📍
                      </button>
                      <button
                        className="action-btn delete-btn"
                        onClick={() => removeBookmark(bookmark.id)}
                        title="Remove bookmark"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === "annotations" && (
          <div className="annotations-section">
            <div className="add-annotation">
              <h3>{editingAnnotation ? "Edit Annotation" : "Add New Annotation"}</h3>
              <input
                type="text"
                placeholder="Annotation title..."
                value={newAnnotation.title}
                onChange={(e) =>
                  setNewAnnotation((prev) => ({ ...prev, title: e.target.value }))
                }
                className="annotation-title-input"
              />
              <textarea
                placeholder="Write your annotation here..."
                value={newAnnotation.content}
                onChange={(e) =>
                  setNewAnnotation((prev) => ({ ...prev, content: e.target.value }))
                }
                className="annotation-content-input"
                rows={4}
              />
              <div className="annotation-form-actions">
                {editingAnnotation ? (
                  <>
                    <button
                      className="btn-primary"
                      onClick={handleUpdateAnnotation}
                      disabled={!newAnnotation.title.trim()}
                    >
                      Update Annotation
                    </button>
                    <button className="btn-secondary" onClick={handleCancelEdit}>
                      Cancel
                    </button>
                  </>
                ) : (
                  <button
                    className="btn-primary"
                    onClick={handleAddAnnotation}
                    disabled={!newAnnotation.title.trim()}
                  >
                    Add Annotation
                  </button>
                )}
              </div>
            </div>

            {annotations.length === 0 ? (
              <div className="empty-state">
                <p>📝 No annotations yet</p>
                <p>Create your first annotation above!</p>
              </div>
            ) : (
              <div className="annotations-list">
                {annotations.map((annotation) => (
                  <div key={annotation.id} className="annotation-item">
                    <div className="annotation-content">
                      <h4 className="annotation-title">{annotation.title}</h4>
                      <p className="annotation-text">{annotation.content}</p>
                      <p className="annotation-meta">
                        Page {annotation.pageNumber} • {formatDate(annotation.createdAt)}
                        {annotation.updatedAt && " • Edited"}
                      </p>
                    </div>
                    <div className="annotation-actions">
                      <button
                        className="action-btn navigate-btn"
                        onClick={() => handleNavigateToAnnotation(annotation)}
                        title="Go to page"
                      >
                        📍
                      </button>
                      <button
                        className="action-btn edit-btn"
                        onClick={() => handleEditAnnotation(annotation)}
                        title="Edit annotation"
                      >
                        ✏️
                      </button>
                      <button
                        className="action-btn delete-btn"
                        onClick={() => removeAnnotation(annotation.id)}
                        title="Delete annotation"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BookmarkAnnotationPanel;
