/* ColorPicker Component Styles */
.color-picker {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-palette {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.color-swatch {
  width: 22px;
  height: 22px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  outline: none;
  position: relative;
  padding: 0;
  margin: 0;
  background: none; /* Override default button background */
}

.color-swatch:hover:not(.disabled) {
  transform: scale(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-swatch:focus:not(.disabled) {
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5);
}

.color-swatch.active-color {
  border-color: #007bff;
  transform: scale(1.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.color-swatch.active-color::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #333;
  font-size: 10px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
}

.color-swatch.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.color-swatch.disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Individual color styles */
.color-swatch.yellow {
  background-color: #ffeb3b !important;
}

.color-swatch.green {
  background-color: #8bc34a !important;
}

.color-swatch.blue {
  background-color: #90caf9 !important;
}

.color-swatch.pink {
  background-color: #f48fb1 !important;
}

.color-swatch.purple {
  background-color: #ce93d8 !important;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .color-swatch {
    transition: none;
  }
  
  .color-swatch:hover:not(.disabled),
  .color-swatch:focus:not(.disabled) {
    transform: none;
  }
  
  .color-swatch.active-color {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .color-swatch {
    border-width: 3px;
  }
  
  .color-swatch.active-color {
    border-color: #000;
  }
  
  .color-swatch:focus:not(.disabled) {
    box-shadow: 0 0 0 3px #000;
  }
}
