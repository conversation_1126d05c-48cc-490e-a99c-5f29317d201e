import React, { useEffect, useRef, useState } from "react";
import { createHighlighter } from "../utils/webHighlightUtils";
import { HIGHLIGHT_COLORS, DEFAULT_HIGHLIGHT_COLOR } from "../utils/constants";

const HighlightReplacementDemo = () => {
  const contentRef = useRef(null);
  const highlighterRef = useRef(null);
  const [selectedColor, setSelectedColor] = useState(DEFAULT_HIGHLIGHT_COLOR);
  const [isReady, setIsReady] = useState(false);
  const [logs, setLogs] = useState([]);

  // Add log entry
  const addLog = (message) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [...prev.slice(-10), `[${timestamp}] ${message}`]);
    console.log(message);
  };

  // Initialize highlighter
  useEffect(() => {
    const initializeHighlighter = async () => {
      if (!contentRef.current) return;

      try {
        addLog("🚀 Initializing Rangy highlighter...");

        const highlighter = await createHighlighter(
          contentRef.current,
          (highlight) => {
            addLog(`🖱️ Clicked highlight: ${highlight.color.name}`);
          },
          (data) => {
            addLog(
              `✨ Created highlight with ${data.sources.length} source(s)`
            );
          },
          () => selectedColor
        );

        if (highlighter) {
          highlighterRef.current = highlighter;
          highlighter.run();
          setIsReady(true);
          addLog("✅ Highlighter ready! Select text to highlight.");
        } else {
          addLog("❌ Failed to create highlighter");
        }
      } catch (error) {
        addLog(`❌ Error initializing highlighter: ${error.message}`);
      }
    };

    initializeHighlighter();

    // Cleanup
    return () => {
      if (highlighterRef.current) {
        highlighterRef.current.stop();
      }
    };
  }, []);

  // Update current color when selection changes
  useEffect(() => {
    if (highlighterRef.current) {
      addLog(`🎨 Selected color: ${selectedColor.name}`);
    }
  }, [selectedColor]);

  const clearAllHighlights = () => {
    if (highlighterRef.current) {
      try {
        // Remove all highlight elements
        const highlightElements = contentRef.current.querySelectorAll(
          '[class*="rangy-highlight-"]'
        );
        highlightElements.forEach((element) => {
          const parent = element.parentNode;
          const textNode = document.createTextNode(element.textContent);
          parent.replaceChild(textNode, element);
          parent.normalize();
        });

        addLog("🧹 All highlights cleared");
      } catch (error) {
        addLog(`❌ Error clearing highlights: ${error.message}`);
      }
    }
  };

  return (
    <div
      style={{
        maxWidth: "800px",
        margin: "0 auto",
        padding: "20px",
        fontFamily: "Arial, sans-serif",
      }}
    >
      <h1>Rangy Highlight Replacement Demo</h1>

      <div
        style={{
          background: "#fff3cd",
          border: "1px solid #ffeaa7",
          padding: "15px",
          borderRadius: "5px",
          margin: "20px 0",
        }}
      >
        <h3>Test Instructions:</h3>
        <ol>
          <li>
            First, select "it is the study of bodies in motion" and highlight it
            with <strong>Yellow</strong>
          </li>
          <li>
            Then, select just the word "study" and highlight it with{" "}
            <strong>Green</strong>
          </li>
          <li>
            The result should be: "it is the" (yellow) + "study" (green) + "of
            bodies in motion" (yellow)
          </li>
          <li>
            Try different combinations to test the replacement functionality
          </li>
        </ol>
      </div>

      <div
        style={{
          margin: "20px 0",
          padding: "15px",
          background: "#e9e9e9",
          borderRadius: "5px",
        }}
      >
        <strong>Select Color: </strong>
        {Object.values(HIGHLIGHT_COLORS).map((color) => (
          <button
            key={color.id}
            onClick={() => setSelectedColor(color)}
            style={{
              padding: "8px 16px",
              margin: "5px",
              border: selectedColor.id === color.id ? "3px solid #333" : "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontWeight: "bold",
              backgroundColor: color.backgroundColor,
              color: "#333",
            }}
          >
            {color.name}
          </button>
        ))}
        <button
          onClick={clearAllHighlights}
          style={{
            marginLeft: "20px",
            padding: "8px 16px",
            background: "#dc3545",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Clear All
        </button>
      </div>

      <div
        ref={contentRef}
        style={{
          background: "#f9f9f9",
          padding: "20px",
          borderRadius: "8px",
          margin: "20px 0",
          fontSize: "18px",
          lineHeight: "1.6",
        }}
      >
        <p>
          Physics is a fundamental science that seeks to understand how the
          universe works. It is the study of bodies in motion, it is divided
          into two parts: classical mechanics and quantum mechanics. Classical
          mechanics deals with the motion of objects that are large enough to be
          seen with the naked eye, while quantum mechanics deals with the motion
          of very small particles like atoms and electrons.
        </p>

        <p>
          The study of physics has led to many important discoveries and
          inventions that have changed the world. From the laws of motion
          discovered by Newton to the theory of relativity by Einstein, physics
          continues to push the boundaries of human knowledge.
        </p>
      </div>

      <div
        style={{
          background: "#f8f9fa",
          border: "1px solid #dee2e6",
          padding: "10px",
          borderRadius: "4px",
          maxHeight: "200px",
          overflowY: "auto",
          fontFamily: "monospace",
          fontSize: "12px",
          marginTop: "20px",
        }}
      >
        <strong>Console Log:</strong>
        {logs.map((log, index) => (
          <div key={index}>{log}</div>
        ))}
      </div>
    </div>
  );
};

export default HighlightReplacementDemo;
