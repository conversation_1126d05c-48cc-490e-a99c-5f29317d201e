import React, { memo, useCallback, useEffect, useRef, useMemo } from "react";
import "./MainContent.css";
import { useAudioStore } from "../stores/audioStore";
import { useHighlightStore } from "../stores/highlightStore";
import LoadingSpinner, { AudioPlayerSkeleton } from "./LoadingSpinner";
import { useErrorHandler } from "../hooks/useErrorHandler";
import { AUDIO_STATES } from "../utils/constants";
import { createHighlighter } from "../utils/webHighlightUtils";
import {
  processContentWithBlocks,
  validateBlockStructure,
} from "../utils/blockProcessor";

const formatTime = (timeInSeconds, showRemaining = false, duration = 0) => {
  if (isNaN(timeInSeconds) || timeInSeconds < 0) return "0:00";
  const time = showRemaining ? duration - timeInSeconds : timeInSeconds;
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  const prefix = showRemaining && time > 0 ? "-" : "";
  return `${prefix}${minutes}:${seconds < 10 ? "0" : ""}${seconds}`;
};

const MainContent = memo(({ topic, textSize }) => {
  // Refs for highlighting
  const contentRef = useRef(null);
  const highlighterRef = useRef(null);

  // Highlighting store (removed selectedColor to prevent re-renders on color change)
  const addHighlight = useHighlightStore((state) => state.addHighlight);
  const getHighlights = useHighlightStore((state) => state.getHighlights);
  const setHighlighter = useHighlightStore((state) => state.setHighlighter);
  const setLoading = useHighlightStore((state) => state.setLoading);
  const setError = useHighlightStore((state) => state.setError);

  // Enhanced audio store usage with error handling
  const isPlaying = useAudioStore((state) => state.isPlaying);
  const currentTime = useAudioStore((state) => state.currentTime);
  const duration = useAudioStore((state) => state.duration);
  const playlist = useAudioStore((state) => state.playlist);
  const trackIndex = useAudioStore((state) => state.trackIndex);
  const audioState = useAudioStore((state) => state.audioState);
  const error = useAudioStore((state) => state.error);
  const isLoading = useAudioStore((state) => state.isLoading);
  const buffered = useAudioStore((state) => state.buffered);
  const play = useAudioStore((state) => state.play);
  const pause = useAudioStore((state) => state.pause);
  const seek = useAudioStore((state) => state.seek);
  const nextTrack = useAudioStore((state) => state.nextTrack);
  const prevTrack = useAudioStore((state) => state.prevTrack);
  const hasNextTrack = useAudioStore((state) => state.hasNextTrack);
  const hasPrevTrack = useAudioStore((state) => state.hasPrevTrack);
  const clearError = useAudioStore((state) => state.clearError);

  // Error handling for component-level errors
  const { addError } = useErrorHandler();

  const currentTrack = playlist[trackIndex] || {};
  const hasAudio = !!currentTrack?.audioSrc;
  const hasMultipleTracks = playlist.length > 1;

  // Highlighting event handlers
  const handleHighlightClick = useCallback((highlightData) => {
    // Handle highlight click events (e.g., show context menu, edit, delete)
    console.log("Highlight clicked:", highlightData);
  }, []);

  // Simple hash function for content identification
  const createSimpleHash = useCallback((str) => {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36).substring(0, 8);
  }, []);

  // Create robust page ID that uniquely identifies this specific page (content-independent)
  const createRobustPageId = useCallback((topic) => {
    if (!topic) return "default";

    // Combine multiple identifiers for maximum uniqueness (but exclude content hash)
    const baseId = topic.id || "unknown";
    const realTopicId = topic.realTopicId || "unknown";
    const title = topic.title || "untitled";
    const contentHeading = topic.content?.heading || "no-heading";

    // Use original structure info for stable identification
    const originalStructure = topic.originalStructure || {};
    const pageNumber = originalStructure.pageNumber || 1;
    const subTopicId = originalStructure.subTopicId || "unknown";

    // Combine stable identifiers (no content hash to allow content changes)
    const robustId = `${baseId}__${realTopicId}__${subTopicId}__page${pageNumber}`;

    console.log(`🔑 Generated stable page ID: ${robustId}`);

    return robustId;
  }, []);

  const handleSelectionCreate = useCallback(
    (data, inst) => {
      if (!topic?.id) {
        console.warn("⚠️ Missing topic ID");
        return;
      }

      try {
        setLoading(true);

        // Create robust page identification
        const topicId = topic.realTopicId || topic.id;
        const pageId = createRobustPageId(topic);

        // The data from web-highlighter CREATE event contains 'sources' array
        const sources = data.sources || [];

        if (sources.length === 0) {
          console.warn("⚠️ No sources found in highlight data");
          return;
        }

        sources.forEach((source) => {
          // Create highlight data for storage (color is already applied by highlighter)
          const highlight = {
            ...source,
            topicId,
            pageId,
            createdAt: new Date().toISOString(),
          };

          // Get the highlighted text from the range
          const highlightedText = source.serializedRange?.text || "N/A";
          console.log(
            `✨ CREATED HIGHLIGHT: "${highlightedText}" on page: ${pageId}`
          );

          try {
            // Add to store
            addHighlight(topicId, pageId, highlight);
          } catch (error) {
            console.error("❌ Failed to add highlight to store:", error);
          }
        });

        // Removed verification logging
      } catch (error) {
        console.error("❌ Failed to create highlight:", error);
        setError("Failed to create highlight");
        addError("Failed to create highlight");
      } finally {
        setLoading(false);
      }
    },
    [topic, addHighlight, setLoading, setError, addError, createRobustPageId]
  );

  // Callback to get current selected color directly from store (no subscription)
  const getCurrentColor = useCallback(() => {
    return useHighlightStore.getState().selectedColor;
  }, []);

  // Initialize highlighter when content is ready
  useEffect(() => {
    console.log("🔍 Highlighter useEffect triggered");
    console.log("📦 Content ref:", contentRef.current);
    console.log("📄 Topic ID:", topic?.id);

    if (!contentRef.current || !topic?.id) {
      console.warn(
        "⚠️ Missing content ref or topic ID, skipping highlighter initialization"
      );
      return;
    }

    // Cleanup previous highlighter
    if (highlighterRef.current) {
      console.log("🧹 Cleaning up previous highlighter");
      highlighterRef.current.stop();
      // Web-highlighter handles cleanup automatically when stopped
    }

    // Clear any existing highlight spans from DOM to prevent cross-page contamination
    if (contentRef.current) {
      const existingHighlights =
        contentRef.current.querySelectorAll(".rangy-highlight");
      console.log(
        `🧹 Removing ${existingHighlights.length} existing highlight spans`
      );
      existingHighlights.forEach((span) => {
        const parent = span.parentNode;
        if (parent) {
          parent.replaceChild(document.createTextNode(span.textContent), span);
          parent.normalize();
        }
      });
    }

    // Log current page identification for debugging
    const currentPageId = createRobustPageId(topic);
    console.log(`🔑 Current page identification: ${currentPageId}`);

    // Add a small delay to ensure DOM is fully rendered
    const initializeHighlighter = async () => {
      console.log("🚀 Initializing web-highlighter...");

      try {
        // Create new highlighter (now async)
        const highlighter = await createHighlighter(
          contentRef.current,
          handleHighlightClick,
          handleSelectionCreate,
          getCurrentColor
        );

        if (highlighter) {
          highlighterRef.current = highlighter;
          setHighlighter(highlighter);

          // Start auto-highlighting - this is crucial!
          console.log("🚀 Starting auto-highlighting...");
          highlighter.run();

          // Restore existing highlights for THIS specific page
          const topicId = topic.realTopicId || topic.id;
          const pageId = createRobustPageId(topic);

          const existingHighlights = getHighlights(topicId, pageId);

          if (existingHighlights.length > 0) {
            console.log(
              `🔄 RESTORING ${existingHighlights.length} highlights on page: ${pageId}`
            );

            // Log each highlight being restored
            existingHighlights.forEach((highlight, index) => {
              const restoredText = highlight.serializedRange?.text || "N/A";
              console.log(
                `🔄 RESTORING HIGHLIGHT ${index + 1}: "${restoredText}"`
              );
            });

            // Restore highlights using web-highlighter's built-in fromStore method
            highlighter.deserialize(existingHighlights);
          } else {
            console.log(`📭 No highlights to restore for page: ${pageId}`);
          }
        }
      } catch (error) {
        console.error("❌ Failed to initialize highlighter:", error);
        setError("Failed to initialize highlighting");
      }
    };

    // Use setTimeout to ensure DOM is ready
    const timeoutId = setTimeout(initializeHighlighter, 100);

    // Cleanup on unmount
    return () => {
      clearTimeout(timeoutId);
      if (highlighterRef.current) {
        console.log("🧹 Cleaning up highlighter...");
        highlighterRef.current.stop(); // Stop auto-highlighting
        // Web-highlighter handles cleanup automatically when stopped
        highlighterRef.current = null;
      }
    };
  }, [
    topic?.id,
    topic?.realTopicId,
    topic?.originalStructure?.pageId,
    handleHighlightClick,
    handleSelectionCreate,
    setHighlighter,
    getHighlights,
  ]); // Removed getCurrentColor to prevent re-initialization on color change

  // Block-based content processing for resilient highlighting
  const getContentHtml = useCallback(() => {
    if (!topic?.content?.body || topic.content.body.length === 0) {
      return "";
    }

    console.log(
      "🔄 Processing content with stable blocks for topic:",
      topic.id
    );

    // Process content with stable block IDs for resilient highlighting
    const topicId = topic.realTopicId || topic.id || "default";
    // Use robust page identification for maximum uniqueness
    const pageId = createRobustPageId(topic);

    console.log(
      `📄 Page-specific processing: topicId="${topicId}", pageId="${pageId}"`
    );

    const processedContent = processContentWithBlocks(
      topic.content.body,
      topicId,
      pageId
    );

    // Validate block structure
    if (!validateBlockStructure(processedContent)) {
      console.warn("⚠️ Content does not have proper block structure");
    } else {
      console.log("✅ Content processed with block structure");
    }

    return processedContent;
  }, [topic?.content?.body, topic?.id]);

  // Memoize the content HTML to prevent unnecessary re-renders
  const contentHtml = useMemo(() => getContentHtml(), [getContentHtml]);

  // Effect to restore highlights after content changes
  useEffect(() => {
    if (!highlighterRef.current || !contentHtml) return;

    // Small delay to ensure DOM is updated
    const restoreTimer = setTimeout(() => {
      const topicId = topic.realTopicId || topic.id;
      const pageId = topic.originalStructure?.pageId || "default";
      const existingHighlights = getHighlights(topicId, pageId);

      if (existingHighlights.length > 0) {
        console.log(
          `🔄 Re-restoring ${existingHighlights.length} highlights after content update`
        );
        highlighterRef.current.deserialize(existingHighlights);
      }
    }, 100);

    return () => clearTimeout(restoreTimer);
  }, [
    contentHtml,
    topic?.id,
    topic?.realTopicId,
    topic?.originalStructure?.pageId,
    getHighlights,
  ]);

  // Handle progress bar click with enhanced error handling
  const handleProgressBarClick = useCallback(
    (event) => {
      if (!hasAudio || !duration) return;

      try {
        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = Math.max(0, Math.min(1, clickX / rect.width));
        const newTime = percentage * duration;

        seek(newTime);
      } catch {
        addError("Failed to seek audio");
      }
    },
    [hasAudio, duration, seek, addError]
  );

  const handlePlayPause = useCallback(async () => {
    if (!hasAudio) return;

    try {
      if (error) {
        clearError();
      }

      if (isPlaying) {
        pause();
      } else {
        await play();
      }
    } catch {
      addError("Audio playback failed");
    }
  }, [hasAudio, isPlaying, play, pause, error, clearError, addError]);

  if (!topic) {
    return (
      <main className="main-content-panel">
        <LoadingSpinner size="large" message="Loading content..." />
      </main>
    );
  }

  const progressPercentage = duration ? (currentTime / duration) * 100 : 0;

  return (
    <main className={`main-content-panel text-size-${textSize}`}>
      {/* Enhanced Audio Player with Loading States and Error Handling */}
      {audioState === AUDIO_STATES.LOADING && !hasAudio ? (
        <AudioPlayerSkeleton />
      ) : (
        <div
          className={`audio-player ${!hasAudio ? "disabled" : ""} ${
            error ? "error" : ""
          }`}
        >
          <div className="player-top-row">
            <div className="audio-title">
              {hasAudio ? (
                <>
                  {currentTrack.title || topic.title}
                  {hasMultipleTracks && (
                    <span className="track-counter">
                      ({trackIndex + 1}/{playlist.length})
                    </span>
                  )}
                </>
              ) : (
                "No Audio for this Topic"
              )}
              {error && (
                <div className="audio-error">
                  <span className="error-icon">⚠</span>
                  <span className="error-text">{error}</span>
                  <button
                    className="error-retry"
                    onClick={clearError}
                    title="Retry"
                  >
                    ↻
                  </button>
                </div>
              )}
            </div>
            <div className="audio-controls">
              {hasMultipleTracks && (
                <button
                  onClick={prevTrack}
                  className="track-nav-btn"
                  disabled={!hasPrevTrack() || isLoading}
                  title="Previous track"
                >
                  ⏮
                </button>
              )}
              <button
                onClick={handlePlayPause}
                className="play-pause-btn-main"
                disabled={!hasAudio || audioState === AUDIO_STATES.LOADING}
                title={isPlaying ? "Pause" : "Play"}
              >
                {audioState === AUDIO_STATES.LOADING ? (
                  <LoadingSpinner size="small" />
                ) : isPlaying && hasAudio ? (
                  "❚❚"
                ) : (
                  "▶"
                )}
              </button>
              {hasMultipleTracks && (
                <button
                  onClick={nextTrack}
                  className="track-nav-btn"
                  disabled={!hasNextTrack() || isLoading}
                  title="Next track"
                >
                  ⏭
                </button>
              )}
            </div>
          </div>
          <div className="player-bottom-row">
            <span className="time-display">
              {hasAudio ? formatTime(currentTime) : "-:--"}
            </span>
            <div
              className="progress-bar-container"
              onClick={handleProgressBarClick}
              role="slider"
              aria-label="Audio progress"
              aria-valuemin="0"
              aria-valuemax={duration || 0}
              aria-valuenow={currentTime || 0}
              tabIndex={hasAudio ? 0 : -1}
            >
              <div className="progress-bar-track">
                {/* Buffered progress */}
                <div
                  className="progress-bar-buffered"
                  style={{ width: `${buffered}%` }}
                />
                {/* Current progress */}
                <div
                  className="progress-bar-current"
                  style={{ width: `${hasAudio ? progressPercentage : 0}%` }}
                />
                {/* Progress handle */}
                {hasAudio && (
                  <div
                    className="progress-handle"
                    style={{ left: `${progressPercentage}%` }}
                  />
                )}
              </div>
            </div>
            <span className="time-display">
              {hasAudio ? formatTime(currentTime, true, duration) : "-:--"}
            </span>
          </div>
        </div>
      )}

      <article className="lesson-content">
        <h1>{topic.content.heading}</h1>

        {/* Render content using simple HTML (no manual IDs) */}
        <div className="lesson-content-body" ref={contentRef}>
          {contentHtml ? (
            <div
              className="html-content"
              dangerouslySetInnerHTML={{ __html: contentHtml }}
            />
          ) : (
            <div className="no-content">
              <p>No content available for this topic.</p>
            </div>
          )}
        </div>
      </article>
    </main>
  );
});

// Display name for debugging
MainContent.displayName = "MainContent";

export default MainContent;
