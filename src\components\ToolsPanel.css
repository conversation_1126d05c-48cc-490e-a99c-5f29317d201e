.tools-panel-sidebar {
  width: 300px;
  height: 100%;
  background-color: #fffbf3;
  padding: 24px;
  border-left: 1px solid #e0e0e0;
  box-sizing: border-box;
  flex-shrink: 0;
  overflow-y: auto;
  scroll-behavior: smooth;
  position: relative; /* For absolute positioning of bookmark button */
  /* Hide scrollbars while maintaining scroll functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.tools-panel-sidebar::-webkit-scrollbar {
  display: none;
}

/* Bookmark Button */
.bookmark-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.bookmark-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.bookmark-btn.bookmarked {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.bookmark-btn.bookmarked:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

/* Custom scrollbar styling for ToolsPanel sidebar */
.tools-panel-sidebar::-webkit-scrollbar {
  width: 6px;
}

.tools-panel-sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.tools-panel-sidebar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.tools-panel-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

.tool-widget {
  margin-bottom: 30px;
}

.tool-widget label {
  display: block;
  font-size: 14px;
  font-weight: bold;
  color: #555;
  margin-bottom: 10px;
}

.tool-widget button {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: #fffbf3;
  cursor: pointer;
  font-size: 14px;
}

.tool-widget button.active {
  background-color: #333;
  color: white;
  border-color: #333;
}

.tool-widget button:hover:not(.active) {
  background-color: #f5f2eb;
  border-color: #bbb;
}

.pagination-controls,
.text-size-controls,
.notes-toolbar {
  display: flex;
  gap: 5px;
  align-items: center;
}

/* Pagination Button Styling - Matching Reference Image */
.pagination-btn {
  padding: 6px 12px !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  background-color: #fffbf3 !important;
  color: #333 !important;
  cursor: pointer;
  font-size: 14px !important;
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-btn:hover:not(.active) {
  background-color: #f5f2eb !important;
  border-color: #bbb !important;
}

.pagination-btn.active {
  background-color: #333 !important;
  color: white !important;
  border-color: #333 !important;
}

/* Pagination Navigation Arrow */
.pagination-nav-btn {
  padding: 6px 8px !important;
  border: 1px solid #ccc !important;
  border-radius: 4px !important;
  background-color: #fffbf3 !important;
  color: #333 !important;
  cursor: pointer;
  font-size: 16px !important;
  font-family: "Montserrat", sans-serif;
  font-weight: 400;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-nav-btn:hover {
  background-color: #f5f2eb !important;
  border-color: #bbb !important;
}

.bookmark-page-btn {
  margin-left: auto !important;
  font-weight: bold;
}

/* MODIFICATION: Updated mini-player styles */
.mini-player {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding: 8px 16px; /* Adjusted padding */
  border: 1px solid #757575; /* Darker border to match image */
  border-radius: 24px; /* Matched border-radius */
  background: transparent; /* Removed background to inherit from parent */
  transition: all 0.3s ease;
}

.mini-player:hover:not(.disabled) {
  border-color: #333; /* Darker border on hover */
}

.mini-player.disabled {
  opacity: 0.6;
  background-color: #f8f8f8;
}

.mini-player.error {
  border-color: #f44336;
  background-color: #fff5f5;
}

/* MODIFICATION: Adjusted title font for closer match */
.mini-player-title {
  font-family: sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #333;
  text-align: left;
  line-height: 1.2;
  margin: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.mini-track-counter {
  font-size: 12px;
  color: #666;
  font-weight: normal;
}

.mini-error {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
  padding: 2px 6px;
  background-color: #ffebee;
  border-radius: 3px;
  font-size: 11px;
}

.mini-error-icon {
  color: #f44336;
  font-weight: bold;
}

.mini-error-text {
  color: #d32f2f;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* MODIFICATION: Adjusted gap and added divider styles */
.mini-player-buttons {
  display: flex;
  gap: 12px; /* Increased gap between controls */
  align-items: center;
  flex-shrink: 0;
}

.mini-player-divider {
  color: #333;
  font-weight: 300;
  font-size: 16px;
}

/* MODIFICATION: Replaced old button styles with new icon-like styles */
.mini-play-pause-btn,
.mini-track-nav-btn {
  background: transparent !important;
  color: #333 !important;
  border: none !important;
  padding: 0;
  margin: 0;
  border-radius: 0;
  width: auto;
  height: auto;
  cursor: pointer;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: sans-serif; /* For better symbol rendering */
  line-height: 1;
  outline: none; /* Remove focus outline */
}

.mini-play-pause-btn {
  font-size: 16px; /* Adjust size for pause/play symbols */
}

.mini-track-nav-btn {
  font-size: 18px; /* Adjust size for track symbols */
}

.mini-play-pause-btn:hover:not(:disabled),
.mini-track-nav-btn:hover:not(:disabled) {
  color: #000 !important; /* Darken on hover */
}

.mini-play-pause-btn:disabled,
.mini-track-nav-btn:disabled {
  color: #bbb !important;
  cursor: not-allowed;
}

/* Color picker styles are now in ColorPicker.css */

textarea {
  width: 100%;
  height: 150px;
  border: 1px solid #ccc;
  border-radius: 6px;
  padding: 10px;
  font-family: inherit;
  font-size: 14px;
  box-sizing: border-box;
  resize: vertical;
}

textarea:focus {
  outline: none;
  border-color: #333;
}
