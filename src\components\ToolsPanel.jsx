import { useState, useCallback, memo, useMemo } from "react";
import "./ToolsPanel.css";
import { useAudioStore } from "../stores/audioStore";
import { useHighlightStore } from "../stores/highlightStore";
import { useBookmarkStore } from "../stores/bookmarkStore";
import { InlineLoader } from "./LoadingSpinner";
import { useErrorHandler } from "../hooks/useErrorHandler";
import { AUDIO_STATES } from "../utils/constants";
import ColorPicker from "./ColorPicker";

const ToolsPanel = memo(
  ({ activeTextSize, onSetTextSize, currentTopic, onPageChange }) => {
    const [noteText, setNoteText] = useState("");

    // Highlighting store
    const selectedColor = useHighlightStore((state) => state.selectedColor);
    const setSelectedColor = useHighlightStore(
      (state) => state.setSelectedColor
    );
    const isHighlightLoading = useHighlightStore((state) => state.isLoading);

    // Bookmark store
    const addBookmark = useBookmarkStore((state) => state.addBookmark);
    const isPageBookmarked = useBookmarkStore(
      (state) => state.isPageBookmarked
    );
    const removeBookmark = useBookmarkStore((state) => state.removeBookmark);
    const bookmarks = useBookmarkStore((state) => state.bookmarks);

    // Enhanced audio store usage
    const isPlaying = useAudioStore((state) => state.isPlaying);
    const playlist = useAudioStore((state) => state.playlist);
    const trackIndex = useAudioStore((state) => state.trackIndex);
    const audioState = useAudioStore((state) => state.audioState);
    const error = useAudioStore((state) => state.error);
    const play = useAudioStore((state) => state.play);
    const pause = useAudioStore((state) => state.pause);
    const nextTrack = useAudioStore((state) => state.nextTrack);
    const prevTrack = useAudioStore((state) => state.prevTrack);
    const hasNextTrack = useAudioStore((state) => state.hasNextTrack);
    const hasPrevTrack = useAudioStore((state) => state.hasPrevTrack);

    // Error handling
    const { addError } = useErrorHandler();

    const currentTrack = playlist[trackIndex] || {};
    const hasAudio = !!currentTrack?.audioSrc;

    // Pagination logic
    const getPaginationInfo = () => {
      if (!currentTopic?.pages || !currentTopic.originalStructure) {
        return { currentPage: 1, totalPages: 1 };
      }

      const currentPageIndex = currentTopic.currentPageIndex || 0;
      const totalPages = currentTopic.originalStructure.totalPages;

      return {
        currentPage: currentPageIndex + 1, // Convert to 1-based
        totalPages: totalPages,
      };
    };

    const { currentPage, totalPages } = getPaginationInfo();

    const handlePageClick = useCallback(
      (pageNumber) => {
        if (currentTopic && onPageChange) {
          try {
            onPageChange(currentTopic.id, pageNumber);
          } catch (error) {
            addError("Failed to navigate to page");
          }
        }
      },
      [currentTopic, onPageChange, addError]
    );

    const handleNextPage = useCallback(() => {
      if (currentPage < totalPages) {
        handlePageClick(currentPage + 1);
      }
    }, [currentPage, totalPages, handlePageClick]);

    const handleTextSizeChange = useCallback(
      (size) => {
        try {
          onSetTextSize(size);
        } catch (error) {
          addError("Failed to change text size");
        }
      },
      [onSetTextSize, addError]
    );

    const handleColorSelect = useCallback(
      (color) => {
        try {
          setSelectedColor(color);
        } catch (error) {
          addError("Failed to change highlight color");
        }
      },
      [setSelectedColor, addError]
    );

    const handleAudioControl = useCallback(
      async (action) => {
        try {
          if (action === "play") {
            await play();
          } else if (action === "pause") {
            pause();
          } else if (action === "next") {
            nextTrack();
          } else if (action === "prev") {
            prevTrack();
          }
        } catch (error) {
          addError("Audio control failed");
        }
      },
      [play, pause, nextTrack, prevTrack, addError]
    );

    // Create robust page ID for bookmarks
    const createRobustPageId = useCallback((topic) => {
      if (!topic) return null;

      const topicId = topic.realTopicId || topic.id;
      const pageIndex = topic.currentPageIndex || 0;

      if (topic.originalStructure) {
        return `${topicId}-${topic.originalStructure.subTopicId}-page-${pageIndex}`;
      }

      return `${topicId}-page-${pageIndex}`;
    }, []);

    // Bookmark functionality
    const handleBookmarkToggle = useCallback(() => {
      if (!currentTopic) return;

      try {
        const topicId = currentTopic.realTopicId || currentTopic.id;
        const pageId = createRobustPageId(currentTopic);
        const pageNumber = (currentTopic.currentPageIndex || 0) + 1;
        const isCurrentlyBookmarked = isPageBookmarked(topicId, pageId);

        console.log("🔖 Bookmark toggle:", {
          topicId,
          pageId,
          pageNumber,
          isCurrentlyBookmarked,
          title: currentTopic.title,
        });

        if (isCurrentlyBookmarked) {
          // Remove bookmark
          const existingBookmark = bookmarks.find(
            (bookmark) =>
              bookmark.topicId === topicId && bookmark.pageId === pageId
          );
          if (existingBookmark) {
            console.log("🗑️ Removing bookmark:", existingBookmark.id);
            removeBookmark(existingBookmark.id);
          }
        } else {
          // Add bookmark
          const newBookmark = {
            topicId,
            pageId,
            title: currentTopic.title,
            pageNumber,
          };
          console.log("➕ Adding bookmark:", newBookmark);
          addBookmark(newBookmark);
        }
      } catch (error) {
        console.error("❌ Bookmark toggle error:", error);
        addError("Failed to toggle bookmark");
      }
    }, [
      currentTopic,
      isPageBookmarked,
      bookmarks,
      removeBookmark,
      addBookmark,
      createRobustPageId,
      addError,
    ]);

    // Check if current page is bookmarked
    const currentPageBookmarked = useMemo(() => {
      if (!currentTopic) return false;
      const topicId = currentTopic.realTopicId || currentTopic.id;
      const pageId = createRobustPageId(currentTopic);
      return isPageBookmarked(topicId, pageId);
    }, [currentTopic, isPageBookmarked, createRobustPageId, bookmarks]);

    return (
      <aside className="tools-panel-sidebar">
        {/* Bookmark Button - Top Right Corner */}
        <button
          className={`bookmark-btn ${
            currentPageBookmarked ? "bookmarked" : ""
          }`}
          onClick={handleBookmarkToggle}
          title={currentPageBookmarked ? "Remove bookmark" : "Add bookmark"}
        >
          {currentPageBookmarked ? "🔖" : "📖"}
        </button>

        {totalPages > 1 && (
          <div className="tool-widget">
            <label>Page:</label>
            <div className="pagination-controls">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (pageNum) => (
                  <button
                    key={pageNum}
                    className={`pagination-btn ${
                      pageNum === currentPage ? "active" : ""
                    }`}
                    onClick={() => handlePageClick(pageNum)}
                    title={`Go to page ${pageNum}`}
                  >
                    {pageNum}
                  </button>
                )
              )}
              {currentPage < totalPages && (
                <button
                  className="pagination-nav-btn"
                  onClick={handleNextPage}
                  title="Next page"
                >
                  ›
                </button>
              )}
            </div>
          </div>
        )}

        <div className="tool-widget">
          <label>Text size:</label>
          <div className="text-size-controls">
            <button
              className={activeTextSize === "small" ? "active" : ""}
              onClick={() => handleTextSizeChange("small")}
              title="Small text size"
            >
              Small
            </button>
            <button
              className={activeTextSize === "normal" ? "active" : ""}
              onClick={() => handleTextSizeChange("normal")}
              title="Normal text size"
            >
              Normal
            </button>
            <button
              className={activeTextSize === "large" ? "active" : ""}
              onClick={() => handleTextSizeChange("large")}
              title="Large text size"
            >
              Large
            </button>
          </div>
        </div>

        <div className="tool-widget">
          <label>Now playing:</label>
          <div
            className={`mini-player ${!hasAudio ? "disabled" : ""} ${
              error ? "error" : ""
            }`}
          >
            <div className="mini-player-title">
              {hasAudio ? (
                <>
                  {currentTrack.title || "Audio"}
                  {playlist.length > 1 && (
                    <span className="mini-track-counter">
                      ({trackIndex + 1}/{playlist.length})
                    </span>
                  )}
                </>
              ) : (
                "No Audio"
              )}
              {error && (
                <div className="mini-error">
                  <span className="mini-error-icon">⚠</span>
                  <span className="mini-error-text">{error}</span>
                </div>
              )}
            </div>
            <div className="mini-player-buttons">
              <button
                onClick={() => handleAudioControl("prev")}
                className={`mini-track-nav-btn ${
                  !hasPrevTrack() ? "disabled" : ""
                }`}
                disabled={
                  !hasPrevTrack() || audioState === AUDIO_STATES.LOADING
                }
                title="Previous track"
              >
                ◀
              </button>
              <button
                onClick={() => handleAudioControl(isPlaying ? "pause" : "play")}
                className={`mini-play-pause-btn ${!hasAudio ? "disabled" : ""}`}
                disabled={!hasAudio}
                title={isPlaying ? "Pause" : "Play"}
              >
                {audioState === AUDIO_STATES.LOADING ? (
                  <InlineLoader size="small" />
                ) : isPlaying && hasAudio ? (
                  "❚❚"
                ) : (
                  "▶"
                )}
              </button>
              <button
                onClick={() => handleAudioControl("next")}
                className={`mini-track-nav-btn ${
                  !hasNextTrack() ? "disabled" : ""
                }`}
                disabled={
                  !hasNextTrack() || audioState === AUDIO_STATES.LOADING
                }
                title="Next track"
              >
                ▶
              </button>
            </div>
          </div>
        </div>

        <div className="tool-widget">
          <label>Highlight Color:</label>
          <ColorPicker
            selectedColor={selectedColor}
            onColorSelect={handleColorSelect}
            disabled={isHighlightLoading}
          />
        </div>

        <div className="tool-widget">
          <label>Notes:</label>
          <textarea
            placeholder="Write your notes here..."
            value={noteText}
            onChange={(e) => setNoteText(e.target.value)}
          />
        </div>
      </aside>
    );
  }
);

// Display name for debugging
ToolsPanel.displayName = "ToolsPanel";

export default ToolsPanel;
