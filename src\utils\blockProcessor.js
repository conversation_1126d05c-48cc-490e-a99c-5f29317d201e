/**
 * Simple Block Processing for Resilient Highlighting
 * Adds stable IDs to content blocks for highlight persistence
 */

/**
 * Generate stable block ID based on content and position
 * @param {string} type - Block type (paragraph, list, etc.)
 * @param {number} index - Block index
 * @param {string} content - Content text for hash generation
 * @param {string} topicId - Topic ID for uniqueness across topics
 * @returns {string} Stable block ID
 */
export const generateBlockId = (
  type = "block",
  index = 0,
  content = "",
  topicId = ""
) => {
  // Create a stable hash based on content + position + topic
  const hashInput = `${topicId}-${type}-${index}-${content.substring(0, 50)}`;
  const hash = createStableHash(hashInput);
  return `${type}-${index}-${hash}`;
};

/**
 * Create a stable hash from input string
 * @param {string} input - Input string to hash
 * @returns {string} Stable hash
 */
const createStableHash = (input) => {
  if (!input) return "default";

  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36).substring(0, 8);
};

/**
 * Process content and add stable block IDs with line-level spans
 * @param {Array} contentBody - Content body array from topic
 * @param {string} topicId - Topic ID for unique line IDs
 * @param {string} pageId - Page ID for unique line IDs
 * @returns {string} HTML with stable block IDs and line spans
 */
export const processContentWithBlocks = (
  contentBody,
  topicId = "",
  pageId = ""
) => {
  if (!Array.isArray(contentBody) || contentBody.length === 0) {
    return "";
  }

  let blockIndex = 0;

  const processedContent = contentBody
    .map((element) => {
      let html = "";

      switch (element.type) {
        case "html":
          // Process raw HTML and add block IDs with line spans
          html = addBlockIdsToHtml(
            element.content || "",
            blockIndex,
            topicId,
            pageId
          );
          blockIndex += countBlockElements(element.content || "");
          break;

        case "paragraph":
          const paragraphContent = element.text || "";
          const paragraphId = generateBlockId(
            "para",
            blockIndex,
            paragraphContent,
            topicId
          );
          // Wrap paragraph content in line span
          const lineId = `${topicId}-${pageId}-line-${blockIndex}`;
          html = `<p id="${paragraphId}" class="highlight-block"><span data-line-id="${lineId}" data-line-index="${blockIndex}" class="highlight-line">${paragraphContent}</span></p>`;
          blockIndex++;
          break;

        case "ordered-list":
          const listId = generateBlockId("list", blockIndex);
          const listItems = (element.items || [])
            .map((item, itemIndex) => {
              const itemId = generateBlockId(
                "item",
                blockIndex + itemIndex + 1
              );
              return `<li id="${itemId}" class="highlight-block">${
                item.label || ""
              } - ${item.description || ""}</li>`;
            })
            .join("");
          html = `<ol id="${listId}" class="highlight-block">${listItems}</ol>`;
          blockIndex += (element.items || []).length + 1;
          break;

        case "unordered-list":
          const ulistId = generateBlockId("list", blockIndex);
          const ulistItems = (element.items || [])
            .map((item, itemIndex) => {
              const itemId = generateBlockId(
                "item",
                blockIndex + itemIndex + 1
              );
              const text = typeof item === "string" ? item : item.text || "";
              return `<li id="${itemId}" class="highlight-block">${text}</li>`;
            })
            .join("");
          html = `<ul id="${ulistId}" class="highlight-block">${ulistItems}</ul>`;
          blockIndex += (element.items || []).length + 1;
          break;

        default:
          console.warn("Unknown content element type:", element.type);
          break;
      }

      return html;
    })
    .join("");

  return processedContent;
};

/**
 * Add line-level spans to HTML content for precise highlighting
 * @param {string} htmlContent - Raw HTML content
 * @param {number} startIndex - Starting block index
 * @param {string} topicId - Topic ID for unique line IDs
 * @param {string} pageId - Page ID for unique line IDs
 * @returns {string} HTML with line-level spans added
 */
export const addBlockIdsToHtml = (
  htmlContent,
  startIndex = 0,
  topicId = "",
  pageId = ""
) => {
  if (!htmlContent) return "";

  try {
    // Create temporary DOM element to parse HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;

    let blockIndex = startIndex;
    let lineIndex = 0;

    // Add IDs to block elements and wrap text in line spans
    const blockElements = tempDiv.querySelectorAll(
      "p, div, h1, h2, h3, h4, h5, h6, li, blockquote, pre"
    );

    blockElements.forEach((element) => {
      if (!element.id) {
        const tagName = element.tagName.toLowerCase();
        const blockId = generateBlockId(tagName, blockIndex, "", topicId);
        element.id = blockId;
        element.classList.add("highlight-block");
        blockIndex++;
      }

      // Process text content and wrap in line spans
      processTextNodesInElement(element, topicId, pageId, lineIndex);
      lineIndex += countLinesInElement(element);
    });

    return tempDiv.innerHTML;
  } catch (error) {
    console.error("Failed to add block IDs to HTML:", error);
    return htmlContent; // Return original on error
  }
};

/**
 * Process text nodes in an element and wrap lines in spans
 * @param {Element} element - DOM element to process
 * @param {string} topicId - Topic ID
 * @param {string} pageId - Page ID
 * @param {number} startLineIndex - Starting line index
 */
const processTextNodesInElement = (
  element,
  topicId,
  pageId,
  startLineIndex
) => {
  const walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_TEXT,
    null,
    false
  );

  const textNodes = [];
  let node;
  while ((node = walker.nextNode())) {
    textNodes.push(node);
  }

  let currentLineIndex = startLineIndex;

  textNodes.forEach((textNode) => {
    const text = textNode.textContent;
    if (!text.trim()) return; // Skip empty text nodes

    // Split text into lines and wrap each in a span
    const lines = text.split("\n");
    const fragment = document.createDocumentFragment();

    lines.forEach((line, index) => {
      if (index > 0) {
        // Add line break for subsequent lines
        fragment.appendChild(document.createTextNode("\n"));
      }

      if (line.trim()) {
        // Create span for non-empty lines
        const lineSpan = document.createElement("span");
        lineSpan.setAttribute(
          "data-line-id",
          `${topicId}-${pageId}-line-${currentLineIndex}`
        );
        lineSpan.setAttribute("data-line-index", currentLineIndex.toString());
        lineSpan.className = "highlight-line";
        lineSpan.textContent = line;
        fragment.appendChild(lineSpan);
        currentLineIndex++;
      } else if (line === "") {
        // Preserve empty lines
        fragment.appendChild(document.createTextNode(line));
      }
    });

    // Replace the original text node with the fragment
    textNode.parentNode.replaceChild(fragment, textNode);
  });
};

/**
 * Count lines in an element
 * @param {Element} element - DOM element
 * @returns {number} Number of lines
 */
const countLinesInElement = (element) => {
  const text = element.textContent || "";
  return text.split("\n").filter((line) => line.trim()).length;
};

/**
 * Count block elements in HTML content
 * @param {string} htmlContent - HTML content
 * @returns {number} Number of block elements
 */
export const countBlockElements = (htmlContent) => {
  if (!htmlContent) return 0;

  try {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = htmlContent;
    const blockElements = tempDiv.querySelectorAll(
      "p, div, h1, h2, h3, h4, h5, h6, li, blockquote, pre"
    );
    return blockElements.length;
  } catch (error) {
    return 0;
  }
};

/**
 * Extract all block IDs from processed content
 * @param {string} processedHtml - HTML content with block IDs
 * @returns {Array} Array of block IDs
 */
export const extractBlockIds = (processedHtml) => {
  if (!processedHtml) return [];

  try {
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = processedHtml;

    const elements = tempDiv.querySelectorAll("[id]");
    return Array.from(elements).map((el) => el.id);
  } catch (error) {
    console.error("Failed to extract block IDs:", error);
    return [];
  }
};

/**
 * Find the block ID for a given DOM node
 * @param {Node} node - DOM node
 * @returns {string|null} Block ID or null if not found
 */
export const findBlockId = (node) => {
  let element = node;

  // Traverse up the DOM tree to find an element with an ID
  while (element && element !== document.body) {
    if (element.nodeType === Node.ELEMENT_NODE && element.id) {
      return element.id;
    }
    element = element.parentNode;
  }

  return null;
};

/**
 * Validate that content has proper block structure
 * @param {string} htmlContent - HTML content to validate
 * @returns {boolean} True if content has proper block structure
 */
export const validateBlockStructure = (htmlContent) => {
  if (!htmlContent) return false;

  try {
    const blockIds = extractBlockIds(htmlContent);
    return blockIds.length > 0;
  } catch (error) {
    console.error("Failed to validate block structure:", error);
    return false;
  }
};

/**
 * Create content hash for change detection
 * @param {string} htmlContent - HTML content
 * @returns {string} Content hash
 */
export const createContentHash = (htmlContent) => {
  if (!htmlContent) return "";

  // Simple hash function for content versioning
  let hash = 0;
  for (let i = 0; i < htmlContent.length; i++) {
    const char = htmlContent.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  return Math.abs(hash).toString(36);
};

/**
 * Check if an element is a block-level element
 * @param {Element} element - DOM element to check
 * @returns {boolean} True if block element
 */
export const isBlockElement = (element) => {
  const blockElements = [
    "div",
    "p",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "ul",
    "ol",
    "li",
    "blockquote",
    "pre",
    "article",
    "section",
    "header",
    "footer",
    "main",
    "aside",
  ];

  return blockElements.includes(element.tagName?.toLowerCase());
};
