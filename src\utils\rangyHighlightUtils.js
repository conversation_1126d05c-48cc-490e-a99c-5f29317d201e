/* eslint-disable */
// @ts-nocheck
// @ts-ignore
/**
 * Web-Highlighter based highlighting implementation
 * Using web-highlighter library for efficient text highlighting with built-in functions only
 * Follows web-highlighter documentation strictly - no text storage for efficiency
 */

import <PERSON>light<PERSON> from "web-highlighter";
import { HIGHLIGHT_COLORS } from "./constants";

/**
 * Efficient highlight data structure using web-highlighter's Source object design
 * No text storage for maximum efficiency - only positional metadata
 */
class HighlightSource {
  constructor(startMeta, endMeta, colorId, id = null) {
    this.id = id || this.generateId();
    this.startMeta = startMeta; // Web-highlighter's start metadata
    this.endMeta = endMeta; // Web-highlighter's end metadata
    this.colorId = colorId; // Color identifier
    this.createdAt = new Date().toISOString();
    // ✅ No text storage - web-highlighter handles this efficiently
  }

  generateId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `highlight-${timestamp}-${random}`;
  }

  /**
   * Convert to JSON for storage (optimized - no text)
   */
  toJSON() {
    return {
      id: this.id,
      startMeta: this.startMeta,
      endMeta: this.endMeta,
      colorId: this.colorId,
      createdAt: this.createdAt,
      // ✅ No text field - web-highlighter manages text efficiently
    };
  }

  /**
   * Create from JSON data (optimized)
   */
  static fromJSON(data) {
    const highlight = new HighlightSource(
      data.startMeta,
      data.endMeta,
      data.colorId,
      data.id
    );
    highlight.createdAt = data.createdAt;
    return highlight;
  }
}

/**
 * Web-Highlighter wrapper class implementing selective highlight replacement
 * Uses only web-highlighter built-in functions as per documentation
 */
class WebHighlighterWrapper {
  constructor(container, onHighlightClick, onSelectionCreate, getCurrentColor) {
    this.container = container;
    this.onHighlightClick = onHighlightClick;
    this.onSelectionCreate = onSelectionCreate;
    this.getCurrentColor = getCurrentColor;
    this.highlighter = null;
    this.highlightStore = new Map(); // Store highlight metadata by ID
    this.currentColorId = "yellow"; // Default color

    console.log("✅ Web-Highlighter wrapper created for container:", container);
  }

  /**
   * Initialize web-highlighter with proper configuration
   */
  async initialize() {
    try {
      // Create web-highlighter instance with configuration
      this.highlighter = new Highlighter({
        $root: this.container,
        exceptSelectors: ["pre", "code", "script", "style"], // Skip these elements
        wrapTag: "span",
        verbose: true, // Enable logging for debugging
        style: {
          className: "web-highlight-wrap", // Base class name
        },
      });

      // Set up event listeners using web-highlighter's built-in events
      this.setupEventListeners();

      console.log("✅ Web-Highlighter initialized successfully");
      return true;
    } catch (error) {
      console.error("❌ Failed to initialize Web-Highlighter:", error);
      return false;
    }
  }

  /**
   * Set up event listeners using web-highlighter's event system
   */
  setupEventListeners() {
    // Listen for highlight creation
    this.highlighter.on(Highlighter.event.CREATE, (data, inst, e) => {
      console.log("🎯 Highlight created:", data);
      this.handleHighlightCreate(data);
    });

    // Listen for highlight clicks
    this.highlighter.on(Highlighter.event.CLICK, (data, inst, e) => {
      console.log("🖱️ Highlight clicked:", data);
      if (this.onHighlightClick) {
        this.onHighlightClick(data);
      }
    });

    // Listen for hover events
    this.highlighter.on(Highlighter.event.HOVER, (data, inst, e) => {
      console.log("🔍 Highlight hovered:", data);
      // Add hover styling using web-highlighter's built-in addClass
      this.highlighter.addClass("highlight-hover", data.id);
    });

    // Listen for hover out events
    this.highlighter.on(Highlighter.event.HOVER_OUT, (data, inst, e) => {
      console.log("🔍 Highlight hover out:", data);
      // Remove hover styling using web-highlighter's built-in removeClass
      this.highlighter.removeClass("highlight-hover", data.id);
    });

    // Listen for highlight removal
    this.highlighter.on(Highlighter.event.REMOVE, (data, inst, e) => {
      console.log("🗑️ Highlight removed:", data);
      // Clean up stored metadata
      if (data.ids) {
        data.ids.forEach((id) => this.highlightStore.delete(id));
      }
    });
  }

  /**
   * Handle highlight creation and implement replacement logic
   */
  handleHighlightCreate(data) {
    const currentColor = this.getCurrentColor();
    if (!currentColor) {
      console.warn("⚠️ No current color available");
      return;
    }

    // Store highlight metadata (no text storage for efficiency)
    const highlightSource = new HighlightSource(
      data.sources[0].startMeta,
      data.sources[0].endMeta,
      currentColor.id,
      data.sources[0].id
    );

    this.highlightStore.set(data.sources[0].id, highlightSource);

    // Apply color styling using web-highlighter's built-in addClass
    this.applyColorStyling(data.sources[0].id, currentColor.id);

    // Notify parent component
    if (this.onSelectionCreate) {
      this.onSelectionCreate({
        sources: [highlightSource.toJSON()],
      });
    }

    console.log("✨ Highlight created and stored:", highlightSource.id);
  }

  /**
   * Apply color styling using web-highlighter's built-in addClass method
   */
  applyColorStyling(highlightId, colorId) {
    const colorClass = `highlight-${colorId}`;
    this.highlighter.addClass(colorClass, highlightId);
    console.log(`🎨 Applied ${colorId} styling to highlight ${highlightId}`);
  }

  /**
   * Implement selective highlight replacement using web-highlighter's built-in functions
   * This is the core functionality for replacing overlapping highlights
   */
  async replaceHighlightSelection() {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    const range = selection.getRangeAt(0);
    if (range.collapsed) return;

    const currentColor = this.getCurrentColor();
    if (!currentColor) {
      console.warn("⚠️ No current color available for replacement");
      return;
    }

    console.log("🎯 Starting selective highlight replacement");

    try {
      // Step 1: Find overlapping highlights using web-highlighter's built-in methods
      const overlappingIds = this.findOverlappingHighlights(range);

      if (overlappingIds.length > 0) {
        console.log(`🔍 Found ${overlappingIds.length} overlapping highlights`);

        // Step 2: Get the DOM nodes for overlapping highlights
        const overlappingDoms = overlappingIds
          .map((id) => this.highlighter.getDoms(id))
          .flat();

        // Step 3: Calculate preserved ranges before removal
        const preservedRanges = this.calculatePreservedRanges(
          overlappingDoms,
          range
        );

        // Step 4: Remove overlapping highlights using web-highlighter's built-in remove
        overlappingIds.forEach((id) => {
          this.highlighter.remove(id);
          this.highlightStore.delete(id);
        });

        // Step 5: Create new highlight for the selection using web-highlighter's fromRange
        this.highlighter.fromRange(range);

        // Step 6: Restore preserved ranges using web-highlighter's fromStore
        this.restorePreservedRanges(preservedRanges);

        console.log("✨ Selective highlight replacement completed");
      } else {
        // No overlapping highlights, just create new highlight
        this.highlighter.fromRange(range);
        console.log("✨ New highlight created (no overlaps)");
      }

      // Clear selection
      selection.removeAllRanges();
    } catch (error) {
      console.error("❌ Error in selective highlight replacement:", error);
    }
  }

  /**
   * Handle text selection and create highlights
   */
  handleSelection() {
    if (!this.isRunning) {
      console.log("🔄 Highlighter not running, skipping selection");
      return;
    }

    // Use native browser selection instead of rangy for now
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      console.log("🔄 No selection found");
      return;
    }

    const range = selection.getRangeAt(0);
    if (range.collapsed) {
      console.log("🔄 Selection is collapsed, skipping");
      return;
    }

    // Check if selection is within our container
    if (!this.container.contains(range.commonAncestorContainer)) {
      console.log("🔄 Selection outside container, skipping");
      return;
    }

    // Get current color
    const currentColor = this.getCurrentColor();
    if (!currentColor) {
      console.warn("⚠️ No current color available for highlighting");
      return;
    }

    console.log("✨ Creating highlight with color:", currentColor.name);

    try {
      // Use web-highlighter's replacement functionality
      this.replaceHighlightSelection();
    } catch (error) {
      console.error("❌ Failed to create highlight:", error);
    }
  }

  /**
   * Find overlapping highlights using web-highlighter's built-in methods
   */
  findOverlappingHighlights(range) {
    const overlappingIds = [];

    // Get all highlight IDs using web-highlighter's getIdsByDom method
    const allIds = this.highlighter.getIdsByDom(this.container);

    allIds.forEach(id => {
      // Get DOM elements for this highlight using web-highlighter's getDoms
      const doms = this.highlighter.getDoms(id);

      // Check if any DOM element overlaps with the selection range
      doms.forEach(dom => {
        if (this.domOverlapsWithRange(dom, range)) {
          if (!overlappingIds.includes(id)) {
            overlappingIds.push(id);
          }
        }
      });
    });

    return overlappingIds;
  }

  /**
   * Check if a DOM element overlaps with a range
   */
  domOverlapsWithRange(dom, range) {
    try {
      const domRange = document.createRange();
      domRange.selectNodeContents(dom);

      // Check if ranges overlap using DOM Range API
      const comparison1 = range.compareBoundaryPoints(Range.START_TO_END, domRange);
      const comparison2 = range.compareBoundaryPoints(Range.END_TO_START, domRange);

      return comparison1 > 0 && comparison2 < 0;
    } catch (error) {
      console.warn("⚠️ Error checking DOM overlap:", error);
      return false;
    }
  }

    // Step 4: Restore non-overlapping parts of removed highlights
    this.restorePreservedRanges(preservedRanges);

    // Step 5: Clear selection
    selection.removeAllRanges();

    console.log("✨ Replacement highlight completed");
  }

  /**
   * Calculate preserved ranges (parts of overlapping highlights that should remain)
   * Uses efficient range calculations without storing text
   */
  calculatePreservedRanges(overlappingDoms, newRange) {
    const preservedRanges = [];

    overlappingDoms.forEach(dom => {
      try {
        const domRange = document.createRange();
        domRange.selectNodeContents(dom);

        // Calculate before range (part before the new selection)
        const beforeRange = this.getRangeBeforeOverlap(domRange, newRange);
        if (beforeRange && !beforeRange.collapsed) {
          preservedRanges.push({
            range: beforeRange,
            colorId: this.getColorIdFromDom(dom),
            position: 'before'
          });
        }

        // Calculate after range (part after the new selection)
        const afterRange = this.getRangeAfterOverlap(domRange, newRange);
        if (afterRange && !afterRange.collapsed) {
          preservedRanges.push({
            range: afterRange,
            colorId: this.getColorIdFromDom(dom),
            position: 'after'
          });
        }

      } catch (error) {
        console.warn("⚠️ Error calculating preserved ranges:", error);
      }
    });

    return preservedRanges;
  }

  /**
   * Get range before overlap (efficient, no text storage)
   */
  getRangeBeforeOverlap(originalRange, newRange) {
    try {
      const beforeRange = document.createRange();
      beforeRange.setStart(originalRange.startContainer, originalRange.startOffset);

      // Check if original starts before new range
      const comparison = originalRange.compareBoundaryPoints(Range.START_TO_START, newRange);
      if (comparison < 0) {
        beforeRange.setEnd(newRange.startContainer, newRange.startOffset);
        return beforeRange;
      }

      return null;
    } catch (error) {
      console.warn("⚠️ Error creating before range:", error);
      return null;
    }
  }

  /**
   * Get range after overlap (efficient, no text storage)
   */
  getRangeAfterOverlap(originalRange, newRange) {
    try {
      const afterRange = document.createRange();

      // Check if original ends after new range
      const comparison = originalRange.compareBoundaryPoints(Range.END_TO_END, newRange);
      if (comparison > 0) {
        afterRange.setStart(newRange.endContainer, newRange.endOffset);
        afterRange.setEnd(originalRange.endContainer, originalRange.endOffset);
        return afterRange;
      }

      return null;
    } catch (error) {
      console.warn("⚠️ Error creating after range:", error);
      return null;
    }
  }

  /**
   * Get color ID from DOM element (extract from class names)
   */
  getColorIdFromDom(dom) {
    const classList = Array.from(dom.classList);
    const colorClass = classList.find(cls => cls.startsWith('highlight-'));
    return colorClass ? colorClass.replace('highlight-', '') : 'yellow';
  }

  /**
   * Restore preserved ranges using web-highlighter's fromRange method
   */
  restorePreservedRanges(preservedRanges) {
    preservedRanges.forEach(preserved => {
      try {
        // Create highlight from range using web-highlighter's built-in method
        this.highlighter.fromRange(preserved.range);

        // Apply color styling
        const lastCreatedId = this.getLastCreatedHighlightId();
        if (lastCreatedId) {
          this.applyColorStyling(lastCreatedId, preserved.colorId);
        }

        console.log(`✅ Restored ${preserved.colorId} highlight (${preserved.position})`);
      } catch (error) {
        console.warn("⚠️ Error restoring preserved range:", error);
      }
    });
  }

  /**
   * Get the last created highlight ID (helper for restoration)
   */
  getLastCreatedHighlightId() {
    const allIds = this.highlighter.getIdsByDom(this.container);
    return allIds[allIds.length - 1] || null;
  }
  applyRangyHighlight(range, color) {
    try {
      const className = `rangy-highlight-${color.id}`;

      // Create a temporary selection with our range
      const selection = rangy.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      // Apply highlight using rangy highlighter
      this.rangyHighlighter.highlightSelection(className);

      // Store highlight data
      const serializedRange = this.serializeRange(range);
      const highlightData = new HighlightData(serializedRange, color);
      this.highlightData.set(highlightData.id, highlightData);

      // Add click handlers to newly created highlight elements
      this.addClickHandlersToHighlights(className, highlightData);

      console.log(`✅ Applied ${color.name} highlight using Rangy`);

      // Notify parent component
      if (this.onSelectionCreate) {
        this.onSelectionCreate({
          sources: [highlightData.toJSON()],
        });
      }
    } catch (error) {
      console.error("❌ Failed to apply Rangy highlight:", error);
    }
  }

  /**
   * Find highlights that overlap with the given range
   */
  findOverlappingHighlights(range) {
    const overlapping = [];
    const rangeStart = range.startOffset;
    const rangeEnd = range.endOffset;
    const rangeContainer = range.commonAncestorContainer;

    // Find all highlight elements within the range
    const highlightElements = this.container.querySelectorAll(
      '[class*="rangy-highlight-"]'
    );

    highlightElements.forEach((element) => {
      try {
        // Create range for this highlight element
        const elementRange = document.createRange();
        elementRange.selectNodeContents(element);

        // Check if ranges overlap
        if (this.rangesOverlap(range, elementRange)) {
          // Get highlight data
          const highlightId = element.getAttribute("data-highlight-id");
          const colorId = this.extractColorFromClassName(element.className);

          overlapping.push({
            element: element,
            range: elementRange,
            highlightId: highlightId,
            colorId: colorId,
            text: element.textContent,
          });
        }
      } catch (error) {
        console.warn("⚠️ Error checking overlap for element:", element, error);
      }
    });

    console.log(`🔍 Found ${overlapping.length} overlapping highlights`);
    return overlapping;
  }

  /**
   * Check if two ranges overlap
   */
  rangesOverlap(range1, range2) {
    try {
      // Use range comparison methods
      const comparison1 = range1.compareBoundaryPoints(
        Range.START_TO_END,
        range2
      );
      const comparison2 = range1.compareBoundaryPoints(
        Range.END_TO_START,
        range2
      );

      // Ranges overlap if start of range1 is before end of range2 AND end of range1 is after start of range2
      return comparison1 > 0 && comparison2 < 0;
    } catch (error) {
      console.warn("⚠️ Error comparing ranges:", error);
      return false;
    }
  }

  /**
   * Extract color ID from class name
   */
  extractColorFromClassName(className) {
    const match = className.match(/rangy-highlight-(\w+)/);
    return match ? match[1] : null;
  }

  /**
   * Remove overlapping highlights and return ranges to preserve
   */
  removeOverlappingHighlights(overlappingHighlights, newRange) {
    const preservedRanges = [];

    overlappingHighlights.forEach((highlight) => {
      try {
        const element = highlight.element;
        const elementRange = highlight.range;
        const colorId = highlight.colorId;

        // Calculate non-overlapping parts
        const beforeRange = this.getRangeBeforeOverlap(elementRange, newRange);
        const afterRange = this.getRangeAfterOverlap(elementRange, newRange);

        // Store ranges to preserve with their color
        if (beforeRange && !beforeRange.collapsed) {
          preservedRanges.push({
            range: beforeRange,
            colorId: colorId,
            text: beforeRange.toString(),
          });
        }

        if (afterRange && !afterRange.collapsed) {
          preservedRanges.push({
            range: afterRange,
            colorId: colorId,
            text: afterRange.toString(),
          });
        }

        // Remove the original highlight element
        this.removeHighlightElement(element);

        console.log(
          `🗑️ Removed overlapping ${colorId} highlight, preserved ${preservedRanges.length} ranges`
        );
      } catch (error) {
        console.warn("⚠️ Error removing overlapping highlight:", error);
      }
    });

    return preservedRanges;
  }

  /**
   * Get the range before the overlap
   */
  getRangeBeforeOverlap(originalRange, newRange) {
    try {
      const beforeRange = document.createRange();
      beforeRange.setStart(
        originalRange.startContainer,
        originalRange.startOffset
      );

      // Set end to start of new range
      if (
        originalRange.compareBoundaryPoints(Range.START_TO_START, newRange) < 0
      ) {
        beforeRange.setEnd(newRange.startContainer, newRange.startOffset);
        return beforeRange;
      }

      return null;
    } catch (error) {
      console.warn("⚠️ Error creating before range:", error);
      return null;
    }
  }

  /**
   * Get the range after the overlap
   */
  getRangeAfterOverlap(originalRange, newRange) {
    try {
      const afterRange = document.createRange();

      // Set start to end of new range
      if (originalRange.compareBoundaryPoints(Range.END_TO_END, newRange) > 0) {
        afterRange.setStart(newRange.endContainer, newRange.endOffset);
        afterRange.setEnd(originalRange.endContainer, originalRange.endOffset);
        return afterRange;
      }

      return null;
    } catch (error) {
      console.warn("⚠️ Error creating after range:", error);
      return null;
    }
  }

  /**
   * Remove a highlight element from DOM
   */
  removeHighlightElement(element) {
    try {
      const parent = element.parentNode;
      if (parent) {
        // Replace highlight element with its text content
        const textNode = document.createTextNode(element.textContent);
        parent.replaceChild(textNode, element);

        // Normalize to merge adjacent text nodes
        parent.normalize();
      }
    } catch (error) {
      console.warn("⚠️ Error removing highlight element:", error);
    }
  }

  /**
   * Restore preserved ranges with their original colors
   */
  restorePreservedRanges(preservedRanges) {
    preservedRanges.forEach((preserved) => {
      try {
        const colorId = preserved.colorId;
        const range = preserved.range;

        // Find the color object
        const color = this.getColorById(colorId);
        if (color) {
          // Apply highlight to preserved range
          this.applyRangyHighlight(range, color);
          console.log(
            `✅ Restored ${colorId} highlight for: "${preserved.text}"`
          );
        }
      } catch (error) {
        console.warn("⚠️ Error restoring preserved range:", error);
      }
    });
  }

  /**
   * Get color object by ID
   */
  getColorById(colorId) {
    const HIGHLIGHT_COLORS = {
      yellow: {
        id: "yellow",
        name: "Yellow",
        color: "#ffeb3b",
        backgroundColor: "rgba(255, 235, 59, 0.3)",
      },
      green: {
        id: "green",
        name: "Green",
        color: "#8bc34a",
        backgroundColor: "rgba(139, 195, 74, 0.3)",
      },
      blue: {
        id: "blue",
        name: "Blue",
        color: "#90caf9",
        backgroundColor: "rgba(144, 202, 249, 0.3)",
      },
      pink: {
        id: "pink",
        name: "Pink",
        color: "#f48fb1",
        backgroundColor: "rgba(244, 143, 177, 0.3)",
      },
      purple: {
        id: "purple",
        name: "Purple",
        color: "#ce93d8",
        backgroundColor: "rgba(206, 147, 216, 0.3)",
      },
    };

    return HIGHLIGHT_COLORS[colorId] || null;
  }

  /**
   * Add click handlers to newly created highlight elements
   */
  addClickHandlersToHighlights(className, highlightData) {
    // Find elements with the specific class that were just created
    const elements = this.container.querySelectorAll(`.${className}`);

    elements.forEach((element) => {
      // Only add handler if it doesn't already have one
      if (!element.hasAttribute("data-click-handler-added")) {
        element.setAttribute("data-highlight-id", highlightData.id);
        element.setAttribute("data-click-handler-added", "true");

        element.addEventListener("click", (e) => {
          e.stopPropagation();
          if (this.onHighlightClick) {
            this.onHighlightClick(highlightData.toJSON());
          }
        });
      }
    });
  }

  /**
   * Apply highlight styling to a range
   */
  applyHighlight(range, highlightData) {
    try {
      // Create highlight span
      const highlightSpan = document.createElement("span");
      highlightSpan.className = "rangy-highlight";
      highlightSpan.setAttribute("data-highlight-id", highlightData.id);
      highlightSpan.setAttribute(
        "data-highlight-color",
        highlightData.color.id
      );
      highlightSpan.style.backgroundColor = highlightData.color.backgroundColor;
      highlightSpan.style.borderBottom = `2px solid ${highlightData.color.color}`;
      highlightSpan.style.cursor = "pointer";

      // Add click handler
      highlightSpan.addEventListener("click", (e) => {
        e.stopPropagation();
        if (this.onHighlightClick) {
          this.onHighlightClick(highlightData.toJSON());
        }
      });

      // Surround range contents with highlight span
      range.surroundContents(highlightSpan);

      // Store element reference
      this.highlightElements.set(highlightData.id, highlightSpan);
    } catch (error) {
      console.error("❌ Failed to apply highlight styling:", error);
      throw error;
    }
  }

  /**
   * Serialize range using Rangy with custom content validation
   */
  serializeRange(range) {
    try {
      // PRIORITIZE Rangy's position-based serialization (most resilient to admin changes)
      if (rangy && rangy.serializePosition) {
        console.log(
          "✅ Using Rangy position serialization (resilient to content changes)"
        );

        const startPos = rangy.serializePosition(
          range.startContainer,
          range.startOffset,
          this.container
        );
        const endPos = rangy.serializePosition(
          range.endContainer,
          range.endOffset,
          this.container
        );

        return {
          type: "rangy-position",
          startPosition: startPos,
          endPosition: endPos,
        };
      }

      // Fallback: Use character offset approach (custom implementation for resilience)
      console.log(
        "✅ Using character offset serialization (resilient fallback)"
      );
      try {
        const textContent = this.container.textContent || "";
        const startOffset = this.getCharacterOffset(
          range.startContainer,
          range.startOffset
        );
        const endOffset = this.getCharacterOffset(
          range.endContainer,
          range.endOffset
        );

        return {
          type: "character-offset",
          startOffset: startOffset,
          endOffset: endOffset,
          textLength: textContent.length, // For validation
        };
      } catch (error) {
        console.warn(
          "⚠️ Character offset serialization failed:",
          error.message
        );
      }

      // Fallback: Use range serialization without checksum
      if (rangy && rangy.serializeRange) {
        console.log("✅ Using Rangy range serialization (fallback)");

        const serialized = rangy.serializeRange(range, true, this.container); // true = omit checksum

        return {
          type: "rangy-native",
          serialized: serialized,
        };
      }

      // Final fallback
      console.warn(
        "⚠️ Rangy serialization not available, using basic approach"
      );
      return {
        type: "basic",
        startXPath: this.getXPathForNode(range.startContainer),
        startOffset: range.startOffset,
        endXPath: this.getXPathForNode(range.endContainer),
        endOffset: range.endOffset,
      };
    } catch (error) {
      console.error("❌ Failed to serialize range:", error);
      return null;
    }
  }

  /**
   * Deserialize range using pure Rangy deserialization (simplified approach)
   */
  deserializeRange(serializedRange) {
    try {
      // PRIORITIZE Rangy position-based deserialization (most resilient)
      if (serializedRange.type === "rangy-position") {
        console.log(
          "✅ Using Rangy position deserialization (resilient to content changes)"
        );
        if (rangy && rangy.deserializePosition) {
          try {
            const startPos = rangy.deserializePosition(
              serializedRange.startPosition,
              this.container
            );
            const endPos = rangy.deserializePosition(
              serializedRange.endPosition,
              this.container
            );

            if (startPos && endPos) {
              const range = document.createRange();
              range.setStart(startPos.node, startPos.offset);
              range.setEnd(endPos.node, endPos.offset);
              console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
              return range;
            }
          } catch (error) {
            console.warn(
              "⚠️ Rangy position deserialization failed:",
              error.message
            );
            console.log("🔄 Falling back to character offset approach...");

            // Try to convert position data to character offset and retry
            if (serializedRange.startPosition && serializedRange.endPosition) {
              try {
                // Extract character offsets from Rangy position strings if possible
                const startOffset = this.extractCharacterOffsetFromPosition(
                  serializedRange.startPosition
                );
                const endOffset = this.extractCharacterOffsetFromPosition(
                  serializedRange.endPosition
                );

                if (startOffset !== null && endOffset !== null) {
                  console.log(
                    `🔄 Attempting character offset fallback: ${startOffset}-${endOffset}`
                  );
                  const startPos = this.getNodeFromCharacterOffset(startOffset);
                  const endPos = this.getNodeFromCharacterOffset(endOffset);

                  if (startPos && endPos) {
                    const range = document.createRange();
                    range.setStart(startPos.node, startPos.offset);
                    range.setEnd(endPos.node, endPos.offset);
                    console.log(
                      `✅ FALLBACK SUCCESSFUL: "${range.toString()}"`
                    );
                    return range;
                  }
                }
              } catch (fallbackError) {
                console.warn(
                  "⚠️ Character offset fallback also failed:",
                  fallbackError.message
                );
              }
            }
          }
        }
      }

      // Handle character offset deserialization (custom resilient approach)
      if (serializedRange.type === "character-offset") {
        console.log(
          "✅ Using character offset deserialization (resilient to content changes)"
        );
        try {
          const currentTextLength = (this.container.textContent || "").length;

          // Basic validation - if text length changed dramatically, skip
          if (Math.abs(currentTextLength - serializedRange.textLength) > 1000) {
            console.warn(
              "⚠️ Document changed too much, skipping highlight restoration"
            );
            return null;
          }

          const startPos = this.getNodeFromCharacterOffset(
            serializedRange.startOffset
          );
          const endPos = this.getNodeFromCharacterOffset(
            serializedRange.endOffset
          );

          if (startPos && endPos) {
            const range = document.createRange();
            range.setStart(startPos.node, startPos.offset);
            range.setEnd(endPos.node, endPos.offset);
            console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
            return range;
          }
        } catch (error) {
          console.warn(
            "⚠️ Could not deserialize character offset:",
            error.message
          );
          return null;
        }
      }

      // Fallback: Handle legacy rangy-native type
      if (serializedRange.type === "rangy-native") {
        console.log("✅ Using Rangy native deserialization (fallback)");
        if (rangy && rangy.deserializeRange) {
          try {
            const range = rangy.deserializeRange(
              serializedRange.serialized,
              this.container
            );
            if (range) {
              console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
              return range;
            }
          } catch (error) {
            console.warn("⚠️ Could not deserialize range:", error.message);
            return null;
          }
        }
      }

      // Legacy support for old rangy-native type (without normalization)
      if (serializedRange.type === "rangy-native") {
        console.log(
          "✅ Using legacy Rangy native deserialization (no checksum)"
        );
        if (rangy && rangy.deserializeRange) {
          try {
            const range = rangy.deserializeRange(
              serializedRange.serialized,
              this.container
            );
            if (range) {
              console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
              return range;
            }
          } catch (error) {
            console.warn("⚠️ Could not deserialize range:", error.message);
            return null;
          }
        }
      }

      // Handle Rangy position-based deserialization
      if (serializedRange.type === "rangy-position") {
        console.log("✅ Using Rangy position deserialization");
        if (rangy && rangy.deserializePosition) {
          const startPos = rangy.deserializePosition(
            serializedRange.startPosition,
            this.container
          );
          const endPos = rangy.deserializePosition(
            serializedRange.endPosition,
            this.container
          );

          if (startPos && endPos) {
            const range = document.createRange();
            range.setStart(startPos.node, startPos.offset);
            range.setEnd(endPos.node, endPos.offset);
            console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
            return range;
          }
        }
      }

      // Legacy support for other types
      if (serializedRange.type === "simple-xpath") {
        console.log("⚠️ Using simple XPath deserialization fallback");
        return this.deserializeSimpleXPath(serializedRange);
      }

      if (serializedRange.type === "block-based") {
        console.log("⚠️ Using block-based deserialization fallback");
        return this.deserializeBlockBased(serializedRange);
      }

      if (
        serializedRange.type === "xpath-simple" ||
        serializedRange.startXPath
      ) {
        console.log("⚠️ Using XPath deserialization");
        const startNode = this.getNodeByXPath(serializedRange.startXPath);
        const endNode = this.getNodeByXPath(serializedRange.endXPath);

        if (!startNode || !endNode) {
          console.warn("⚠️ Could not find nodes for range restoration");
          return null;
        }

        const range = document.createRange();
        range.setStart(startNode, serializedRange.startOffset);
        range.setEnd(endNode, serializedRange.endOffset);

        return range;
      }

      // Legacy support: Handle old string-based Rangy serialization
      if (
        typeof serializedRange === "string" &&
        rangy &&
        rangy.deserializeRange
      ) {
        console.log("⚠️ Using legacy Rangy deserialization");
        return rangy.deserializeRange(serializedRange, this.container);
      }

      console.warn("⚠️ Invalid serialized range format");
      return null;
    } catch (error) {
      console.error("❌ Failed to deserialize range:", error);
      return null;
    }
  }

  /**
   * Deserialize simple XPath range data with text validation
   */
  deserializeSimpleXPath(serializedRange) {
    try {
      const { startXPath, endXPath, startOffset, endOffset, text } =
        serializedRange;

      // Get nodes from XPath
      const startNode = this.getNodeByXPath(startXPath);
      const endNode = this.getNodeByXPath(endXPath);

      if (!startNode || !endNode) {
        console.warn("⚠️ Could not find nodes from XPath");
        return null;
      }

      // Find text nodes
      const startTextNode = this.findTextNodeInElement(startNode);
      const endTextNode = this.findTextNodeInElement(endNode);

      if (!startTextNode || !endTextNode) {
        console.warn("⚠️ Could not find text nodes");
        return null;
      }

      // Validate text if available
      if (text && startTextNode === endTextNode) {
        const currentContent = startTextNode.textContent;
        const potentialText = currentContent.substring(startOffset, endOffset);

        if (potentialText !== text) {
          console.warn(
            `⚠️ TEXT MISMATCH: Expected "${text}", found "${potentialText}"`
          );

          // Try to find the text in the current content
          const correctIndex = currentContent.indexOf(text);
          if (correctIndex !== -1) {
            const newEndOffset = correctIndex + text.length;
            console.log(
              `✅ SMART MATCH: Found "${text}" at position ${correctIndex}-${newEndOffset}`
            );

            const range = document.createRange();
            range.setStart(startTextNode, correctIndex);
            range.setEnd(startTextNode, newEndOffset);
            console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
            return range;
          }

          console.warn(`❌ Could not find "${text}" in current content`);
          return null;
        }
      }

      // Create range with original offsets
      const range = document.createRange();
      range.setStart(startTextNode, startOffset);
      range.setEnd(endTextNode, endOffset);

      console.log(`✅ SUCCESSFULLY RESTORED: "${range.toString()}"`);
      return range;
    } catch (error) {
      console.error("❌ Failed to deserialize simple XPath range:", error);
      return null;
    }
  }

  /**
   * Deserialize block-based range data
   */
  deserializeBlockBased(serializedRange) {
    try {
      console.log("🔍 Attempting to restore block-based highlight:", {
        startBlockId: serializedRange.startBlockId,
        endBlockId: serializedRange.endBlockId,
        startOffset: serializedRange.startOffset,
        endOffset: serializedRange.endOffset,
        storedText: serializedRange.text || "N/A",
      });

      // Check if block elements exist
      const startBlockElement = document.getElementById(
        serializedRange.startBlockId
      );
      const endBlockElement = document.getElementById(
        serializedRange.endBlockId
      );

      if (!startBlockElement) {
        console.warn(
          `⚠️ Start block element not found: ${serializedRange.startBlockId}`
        );
        console.log(
          "🔍 Available block IDs:",
          Array.from(document.querySelectorAll("[id]"))
            .map((el) => el.id)
            .filter((id) => id.includes("-"))
        );
        console.log(
          "🔍 Looking for block pattern:",
          serializedRange.startBlockId
        );
        return null;
      }

      if (!endBlockElement) {
        console.warn(
          `⚠️ End block element not found: ${serializedRange.endBlockId}`
        );
        return null;
      }

      // For simple block-based highlights within the same block
      if (serializedRange.startBlockId === serializedRange.endBlockId) {
        const textContent = startBlockElement.textContent || "";

        // Find the text node within the block
        const textNode = this.findTextNodeInElement(startBlockElement);
        if (!textNode) {
          console.warn("⚠️ No text node found in block element");
          return null;
        }

        // Validate offsets to prevent IndexSizeError
        const nodeLength = textNode.textContent.length;
        const startOffset = serializedRange.startOffset;
        const endOffset = serializedRange.endOffset;

        // Check if offsets are valid for this node
        if (startOffset < 0 || endOffset < 0) {
          console.warn(
            `⚠️ Negative offsets: start(${startOffset}), end(${endOffset})`
          );
          return null;
        }

        if (startOffset >= endOffset) {
          console.warn(
            `⚠️ Invalid offsets: start(${startOffset}) >= end(${endOffset})`
          );
          return null;
        }

        if (endOffset > nodeLength) {
          console.warn(
            `⚠️ End offset (${endOffset}) exceeds node length (${nodeLength})`
          );
          console.log(`📝 Node content: "${textNode.textContent}"`);
          console.log(
            `📝 Expected text length: ${endOffset}, Actual length: ${nodeLength}`
          );

          // Try smart content matching before giving up
          const smartMatch = this.trySmartContentMatching(
            textNode.textContent,
            serializedRange,
            startOffset,
            endOffset
          );

          if (smartMatch) {
            console.log(`✅ Smart content matching succeeded`);
            return smartMatch;
          }

          // This indicates content has changed - highlight is no longer valid
          console.log(
            `🔄 Content appears to have changed, skipping highlight restoration`
          );
          return null;
        }

        if (startOffset > nodeLength) {
          console.warn(
            `⚠️ Start offset (${startOffset}) exceeds node length (${nodeLength})`
          );
          return null;
        }

        // Use stored offsets directly - Rangy handles content changes internally

        const range = document.createRange();
        range.setStart(textNode, startOffset);
        range.setEnd(textNode, endOffset);

        const restoredText = range.toString();
        console.log(`✅ SUCCESSFULLY RESTORED: "${restoredText}"`);
        return range;
      }

      // For cross-block highlights (more complex)
      console.warn("⚠️ Cross-block highlights not yet implemented");
      return null;
    } catch (error) {
      console.error("❌ Failed to deserialize block-based range:", error);
      return null;
    }
  }

  /**
   * Try smart content matching when exact offsets fail
   */
  trySmartContentMatching(
    currentContent,
    serializedRange,
    originalStartOffset,
    originalEndOffset
  ) {
    try {
      // If we have stored text, try to find it in the current content
      const storedText = serializedRange.text;
      if (!storedText || storedText === "N/A") {
        console.log(
          "📝 No stored text available for smart matching - trying fallback strategy"
        );
        return this.tryFallbackMatching(
          currentContent,
          serializedRange,
          originalStartOffset,
          originalEndOffset
        );
      }

      // Try to find the stored text in the current content
      const foundIndex = currentContent.indexOf(storedText);
      if (foundIndex !== -1) {
        console.log(
          `📝 Found stored text "${storedText}" at index ${foundIndex}`
        );

        // Create range with the found position
        const range = document.createRange();
        const blockElement = document.getElementById(
          serializedRange.startBlockId
        );
        const textNode = this.findTextNodeInElement(blockElement);

        if (textNode) {
          const newEndOffset = foundIndex + storedText.length;
          if (newEndOffset <= textNode.textContent.length) {
            range.setStart(textNode, foundIndex);
            range.setEnd(textNode, newEndOffset);
            console.log(
              `✅ Smart matching: repositioned highlight to ${foundIndex}-${newEndOffset}`
            );
            return range;
          }
        }
      }

      // Try partial matching if exact text not found
      if (storedText.length > 3) {
        const partialText = storedText.substring(
          0,
          Math.min(storedText.length, 10)
        );
        const partialIndex = currentContent.indexOf(partialText);
        if (partialIndex !== -1) {
          console.log(
            `📝 Found partial text "${partialText}" at index ${partialIndex}`
          );

          const range = document.createRange();
          const blockElement = document.getElementById(
            serializedRange.startBlockId
          );
          const textNode = this.findTextNodeInElement(blockElement);

          if (textNode) {
            const newEndOffset = Math.min(
              partialIndex + storedText.length,
              textNode.textContent.length
            );
            if (newEndOffset > partialIndex) {
              range.setStart(textNode, partialIndex);
              range.setEnd(textNode, newEndOffset);
              console.log(
                `✅ Partial matching: repositioned highlight to ${partialIndex}-${newEndOffset}`
              );
              return range;
            }
          }
        }
      }

      console.log("📝 Smart content matching failed - no suitable match found");
      return null;
    } catch (error) {
      console.error("❌ Smart content matching error:", error);
      return null;
    }
  }

  /**
   * Create content fingerprint for admin change detection
   */
  createContentFingerprint() {
    try {
      // Get semantic content that doesn't change with React re-renders
      const textContent = this.container.textContent || "";
      const elementCount = this.container.querySelectorAll("*").length;
      const paragraphCount = this.container.querySelectorAll(
        "p, div, li, h1, h2, h3, h4, h5, h6"
      ).length;

      // Create a hash of the meaningful content
      let hash = 0;
      const contentString = `${
        textContent.length
      }-${elementCount}-${paragraphCount}-${textContent.substring(0, 200)}`;
      for (let i = 0; i < contentString.length; i++) {
        const char = contentString.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32-bit integer
      }

      return {
        textLength: textContent.length,
        elementCount: elementCount,
        paragraphCount: paragraphCount,
        contentHash: Math.abs(hash).toString(36),
        firstWords: textContent.substring(0, 100).trim(),
        lastWords: textContent
          .substring(Math.max(0, textContent.length - 100))
          .trim(),
      };
    } catch (error) {
      console.warn("⚠️ Could not create content fingerprint:", error);
      return null;
    }
  }

  /**
   * Check if content has changed significantly (admin modifications)
   */
  hasContentChangedSignificantly(storedFingerprint) {
    if (!storedFingerprint) return false; // No fingerprint to compare

    const currentFingerprint = this.createContentFingerprint();
    if (!currentFingerprint) return false; // Can't determine

    // Check for significant changes that indicate admin modifications
    const textLengthChanged =
      Math.abs(currentFingerprint.textLength - storedFingerprint.textLength) >
      100;
    const structureChanged =
      Math.abs(
        currentFingerprint.elementCount - storedFingerprint.elementCount
      ) > 10;
    const contentChanged =
      currentFingerprint.contentHash !== storedFingerprint.contentHash;
    const firstWordsChanged =
      currentFingerprint.firstWords !== storedFingerprint.firstWords;

    return (
      textLengthChanged ||
      structureChanged ||
      (contentChanged && firstWordsChanged)
    );
  }

  /**
   * Create normalized container for React-compatible checksum validation
   * This removes React-specific attributes and normalizes the DOM structure
   */
  createNormalizedContainer() {
    try {
      // Clone the container
      const clone = this.container.cloneNode(true);

      // Remove React-specific attributes that change on re-renders
      this.normalizeElement(clone);

      return clone;
    } catch (error) {
      console.warn(
        "⚠️ Could not create normalized container, using original:",
        error
      );
      return this.container;
    }
  }

  /**
   * Normalize an element by removing React-specific attributes
   */
  normalizeElement(element) {
    // Remove React-specific attributes
    const reactAttributes = [
      "data-reactroot",
      "data-react-checksum",
      "data-reactid",
      "data-react-cache-id",
    ];

    reactAttributes.forEach((attr) => {
      if (element.hasAttribute && element.hasAttribute(attr)) {
        element.removeAttribute(attr);
      }
    });

    // Remove any attributes that start with '__react' or contain random IDs
    if (element.attributes) {
      const attributesToRemove = [];
      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i];
        if (
          attr.name.startsWith("__react") ||
          attr.name.includes("_react") ||
          /^data-.*-\w{8,}$/.test(attr.name)
        ) {
          // Remove attributes with random suffixes
          attributesToRemove.push(attr.name);
        }
      }
      attributesToRemove.forEach((attrName) =>
        element.removeAttribute(attrName)
      );
    }

    // Recursively normalize child elements
    if (element.children) {
      for (let i = 0; i < element.children.length; i++) {
        this.normalizeElement(element.children[i]);
      }
    }
  }

  /**
   * Extract character offset from Rangy position string (for fallback)
   */
  extractCharacterOffsetFromPosition(positionString) {
    try {
      // Rangy position strings often contain character offset information
      // Try to parse common formats like "4:15" or similar
      if (typeof positionString === "string") {
        // Look for patterns like "nodeIndex:offset" or just numbers
        const match =
          positionString.match(/(\d+):(\d+)/) || positionString.match(/(\d+)/);
        if (match) {
          // If we have nodeIndex:offset, try to calculate character position
          if (match[2] !== undefined) {
            const nodeIndex = parseInt(match[1]);
            const offset = parseInt(match[2]);
            // This is a simplified approach - in reality we'd need to traverse to the specific node
            return nodeIndex * 50 + offset; // Rough estimate
          } else {
            return parseInt(match[1]);
          }
        }
      }
      return null;
    } catch (error) {
      console.warn(
        "⚠️ Could not extract character offset from position:",
        error
      );
      return null;
    }
  }

  /**
   * Get character offset of a position within the container (for resilient highlighting)
   */
  getCharacterOffset(node, offset) {
    let charOffset = 0;
    const walker = document.createTreeWalker(
      this.container,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let currentNode;
    while ((currentNode = walker.nextNode())) {
      if (currentNode === node) {
        return charOffset + offset;
      }
      charOffset += currentNode.textContent.length;
    }

    return charOffset;
  }

  /**
   * Get node and offset from character offset (for resilient highlighting)
   */
  getNodeFromCharacterOffset(charOffset) {
    let currentOffset = 0;
    const walker = document.createTreeWalker(
      this.container,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let currentNode;
    while ((currentNode = walker.nextNode())) {
      const nodeLength = currentNode.textContent.length;
      if (currentOffset + nodeLength >= charOffset) {
        return {
          node: currentNode,
          offset: charOffset - currentOffset,
        };
      }
      currentOffset += nodeLength;
    }

    return null;
  }

  /**
   * Find the first text node within an element
   */
  findTextNodeInElement(element) {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    return walker.nextNode();
  }

  /**
   * Get node by block-based XPath
   */
  getNodeByBlockXPath(xpath) {
    if (!xpath || !xpath.startsWith("#")) return null;

    try {
      // Parse block-based XPath: #blockId/path/to/node
      const parts = xpath.split("/");
      const blockId = parts[0].substring(1); // Remove #
      const blockElement = document.getElementById(blockId);

      if (!blockElement) {
        console.warn(`⚠️ Block element not found: ${blockId}`);
        return null;
      }

      if (parts.length === 1) {
        return blockElement;
      }

      // Evaluate relative XPath within the block
      const relativePath = parts.slice(1).join("/");
      const result = document.evaluate(
        relativePath,
        blockElement,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      );

      return result.singleNodeValue;
    } catch (error) {
      console.error("❌ Block XPath evaluation failed:", error);
      return null;
    }
  }

  /**
   * Find block ID for a given DOM node
   */
  findBlockId(node) {
    let element = node;

    // Traverse up the DOM tree to find an element with an ID
    while (element && element !== this.container) {
      if (element.nodeType === Node.ELEMENT_NODE && element.id) {
        return element.id;
      }
      element = element.parentNode;
    }

    return null;
  }

  /**
   * Get block-based XPath relative to a block element
   */
  getBlockBasedXPath(node, blockId) {
    const blockElement = document.getElementById(blockId);
    if (!blockElement) return "";

    // Get XPath relative to the block element
    const path = [];
    let current = node;

    while (current && current !== blockElement) {
      let index = 1;
      let sibling = current.previousSibling;

      while (sibling) {
        if (
          sibling.nodeType === current.nodeType &&
          (current.nodeType !== Node.ELEMENT_NODE ||
            sibling.tagName === current.tagName)
        ) {
          index++;
        }
        sibling = sibling.previousSibling;
      }

      const tagName =
        current.nodeType === Node.ELEMENT_NODE
          ? current.tagName.toLowerCase()
          : "text()";
      path.unshift(`${tagName}[${index}]`);
      current = current.parentNode;
    }

    return `#${blockId}/${path.join("/")}`;
  }

  /**
   * Get XPath for a node relative to container (fallback method)
   */
  getXPathForNode(node) {
    if (!node || node === this.container) return "";

    const path = [];
    let current = node;

    while (current && current !== this.container) {
      let index = 1;
      let sibling = current.previousSibling;

      while (sibling) {
        if (
          sibling.nodeType === current.nodeType &&
          (current.nodeType !== Node.ELEMENT_NODE ||
            sibling.tagName === current.tagName)
        ) {
          index++;
        }
        sibling = sibling.previousSibling;
      }

      const tagName =
        current.nodeType === Node.ELEMENT_NODE
          ? current.tagName.toLowerCase()
          : "text()";
      path.unshift(`${tagName}[${index}]`);
      current = current.parentNode;
    }

    return path.join("/");
  }

  /**
   * Get node by XPath using document.evaluate()
   */
  getNodeByXPath(xpath) {
    if (!xpath) return this.container;

    try {
      const result = document.evaluate(
        xpath,
        this.container,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      );

      return result.singleNodeValue;
    } catch (error) {
      console.error("❌ XPath evaluation failed:", error);
      return null;
    }
  }

  /**
   * Restore highlights from stored data
   */
  restoreHighlights(highlightsData) {
    if (!Array.isArray(highlightsData)) return;

    console.log(`📚 Restoring ${highlightsData.length} highlights`);

    highlightsData.forEach((data) => {
      try {
        const highlightData = HighlightData.fromJSON(data);
        this.restoreHighlight(highlightData);
      } catch (error) {
        console.error("❌ Failed to restore highlight:", data.id, error);
      }
    });
  }

  /**
   * Restore a single highlight using document.evaluate() for resilience
   */
  restoreHighlight(highlightData) {
    if (!rangy) {
      console.error("❌ Rangy not loaded, cannot restore highlight");
      return;
    }

    try {
      // Deserialize range using XPath + Offset approach
      const range = this.deserializeRange(highlightData.serializedRange);

      if (range) {
        // Apply highlight styling
        this.applyHighlight(range, highlightData);

        // Store highlight data
        this.highlightData.set(highlightData.id, highlightData);

        console.log("✅ Highlight restored:", highlightData.id);
      } else {
        console.warn(
          "⚠️ Could not restore highlight (range not found):",
          highlightData.id
        );
      }
    } catch (error) {
      console.error("❌ Failed to restore highlight:", highlightData.id, error);
    }
  }

  /**
   * Remove a highlight by ID
   */
  removeHighlight(highlightId) {
    const element = this.highlightElements.get(highlightId);
    if (element && element.parentNode) {
      // Replace highlight span with its text content
      const parent = element.parentNode;
      const textNode = document.createTextNode(element.textContent);
      parent.replaceChild(textNode, element);

      // Normalize text nodes
      parent.normalize();
    }

    // Clean up stored data
    this.highlightData.delete(highlightId);
    this.highlightElements.delete(highlightId);

    console.log("🗑️ Highlight removed:", highlightId);
  }

  /**
   * Start highlighting (enable selection handling)
   */
  run() {
    this.isRunning = true;
    console.log("🚀 Rangy highlighter started");
  }

  /**
   * Stop highlighting (disable selection handling)
   */
  stop() {
    this.isRunning = false;

    // Remove event listeners
    if (this.selectionHandler) {
      document.removeEventListener("mouseup", this.selectionHandler);
      document.removeEventListener("keyup", this.selectionHandler);
    }

    console.log("⏹️ Rangy highlighter stopped");
  }

  /**
   * Get highlighted text dynamically from DOM (no storage needed)
   */
  getHighlightedText(highlightData) {
    try {
      if (highlightData.serializedRange?.type === "block-based") {
        const blockElement = document.getElementById(
          highlightData.serializedRange.startBlockId
        );
        if (!blockElement) return "";

        const textContent = blockElement.textContent || "";
        return textContent.substring(
          highlightData.serializedRange.startOffset,
          highlightData.serializedRange.endOffset
        );
      }

      // For other types, try to restore range and get text
      const range = this.deserializeRange(highlightData.serializedRange);
      return range ? range.toString() : "";
    } catch (error) {
      console.error("❌ Failed to get highlighted text:", error);
      return "";
    }
  }

  /**
   * Get all highlights data (optimized - no text storage)
   */
  getAllHighlights() {
    return Array.from(this.highlightData.values()).map((data) => data.toJSON());
  }
}

/**
 * Create a new Rangy highlighter instance
 */
export const createHighlighter = async (
  container,
  onHighlightClick,
  onSelectionCreate,
  getCurrentColor
) => {
  try {
    console.log("🔧 Creating Rangy highlighter for container:", container);

    const wrapper = new RangyHighlighterWrapper(
      container,
      onHighlightClick,
      onSelectionCreate,
      getCurrentColor
    );

    // Initialize the highlighter
    const success = await wrapper.initialize();

    if (success) {
      return wrapper;
    } else {
      console.error("❌ Failed to initialize Rangy highlighter wrapper");
      return null;
    }
  } catch (error) {
    console.error("❌ Failed to create Rangy highlighter:", error);
    return null;
  }
};

/**
 * Restore highlights using Rangy deserializer
 */
export const restoreHighlights = (highlighter, highlightsData) => {
  if (!highlighter || !Array.isArray(highlightsData)) return;

  highlighter.restoreHighlights(highlightsData);
};

/**
 * Cleanup highlighter instance
 */
export const cleanupHighlighter = (highlighter) => {
  if (!highlighter) return;

  try {
    highlighter.stop();
    console.log("🧹 Rangy highlighter cleaned up");
  } catch (error) {
    console.error("❌ Error during highlighter cleanup:", error);
  }
};

export { HighlightData };
