/* eslint-disable */
// @ts-nocheck
// @ts-ignore
/**
 * Web-Highlighter based highlighting implementation
 * Using web-highlighter library for efficient text highlighting with built-in functions only
 * Follows web-highlighter documentation strictly - no text storage for efficiency
 */

// Import web-highlighter using dynamic import to handle module resolution
let Highlighter = null;

// Load web-highlighter dynamically
const loadWebHighlighter = async () => {
  if (Highlighter) return Highlighter;

  try {
    // Try different import methods
    const module = await import("web-highlighter");
    Highlighter = module.default || module.Highlighter || module;

    if (!Highlighter || typeof Highlighter !== "function") {
      throw new Error("Web-highlighter not found or not a constructor");
    }

    console.log("✅ Web-highlighter loaded successfully");
    return Highlighter;
  } catch (error) {
    console.error("❌ Failed to load web-highlighter:", error);
    throw error;
  }
};

import { HIGHLIGHT_COLORS } from "./constants.js";

/**
 * Efficient highlight data structure using web-highlighter's Source object design
 * No text storage for maximum efficiency - only positional metadata
 */
class HighlightSource {
  constructor(startMeta, endMeta, colorId, id = null) {
    this.id = id || this.generateId();
    this.startMeta = startMeta; // Web-highlighter's start metadata
    this.endMeta = endMeta; // Web-highlighter's end metadata
    this.colorId = colorId; // Color identifier
    this.createdAt = new Date().toISOString();
    // ✅ No text storage - web-highlighter handles this efficiently
  }

  generateId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    return `highlight-${timestamp}-${random}`;
  }

  /**
   * Convert to JSON for storage (optimized - no text)
   */
  toJSON() {
    return {
      id: this.id,
      startMeta: this.startMeta,
      endMeta: this.endMeta,
      colorId: this.colorId,
      createdAt: this.createdAt,
      // ✅ No text field - web-highlighter manages text efficiently
    };
  }

  /**
   * Create from JSON data (optimized)
   */
  static fromJSON(data) {
    const highlight = new HighlightSource(
      data.startMeta,
      data.endMeta,
      data.colorId,
      data.id
    );
    highlight.createdAt = data.createdAt;
    return highlight;
  }
}

/**
 * Web-Highlighter wrapper class implementing selective highlight replacement
 * Uses only web-highlighter built-in functions as per documentation
 */
class WebHighlighterWrapper {
  constructor(container, onHighlightClick, onSelectionCreate, getCurrentColor) {
    this.container = container;
    this.onHighlightClick = onHighlightClick;
    this.onSelectionCreate = onSelectionCreate;
    this.getCurrentColor = getCurrentColor;
    this.highlighter = null;
    this.highlightStore = new Map(); // Store highlight metadata by ID
    this.isRunning = false;
    this.selectionHandler = null;
    this.floatingButton = null; // Floating button for unhighlighting
    this.currentSelection = null; // Track current selection

    console.log("✅ Web-Highlighter wrapper created for container:", container);
  }

  /**
   * Initialize web-highlighter with proper configuration
   */
  async initialize() {
    try {
      // Load web-highlighter dynamically
      const HighlighterClass = await loadWebHighlighter();

      // Create web-highlighter instance with configuration
      this.highlighter = new HighlighterClass({
        $root: this.container,
        exceptSelectors: ["pre", "code", "script", "style"], // Skip these elements
        wrapTag: "span",
        verbose: true, // Enable logging for debugging
        style: {
          className: "web-highlight-wrap", // Base class name
        },
      });

      // Set up event listeners using web-highlighter's built-in events
      this.setupEventListeners();

      console.log("✅ Web-Highlighter initialized successfully");
      return true;
    } catch (error) {
      console.error("❌ Failed to initialize Web-Highlighter:", error);
      return false;
    }
  }

  /**
   * Set up event listeners using web-highlighter's event system
   */
  setupEventListeners() {
    // Get the event constants from the loaded Highlighter class
    const EventType = Highlighter.event;

    // Listen for highlight creation
    this.highlighter.on(EventType.CREATE, (data, inst, e) => {
      console.log("🎯 Highlight created:", data);
      this.handleHighlightCreate(data);
    });

    // Listen for highlight clicks
    this.highlighter.on(EventType.CLICK, (data, inst, e) => {
      console.log("🖱️ Highlight clicked:", data);
      if (this.onHighlightClick) {
        this.onHighlightClick(data);
      }
    });

    // Listen for hover events
    this.highlighter.on(EventType.HOVER, (data, inst, e) => {
      console.log("🔍 Highlight hovered:", data);
      // Add hover styling using web-highlighter's built-in addClass
      this.highlighter.addClass("highlight-hover", data.id);
    });

    // Listen for hover out events
    this.highlighter.on(EventType.HOVER_OUT, (data, inst, e) => {
      console.log("🔍 Highlight hover out:", data);
      // Remove hover styling using web-highlighter's built-in removeClass
      this.highlighter.removeClass("highlight-hover", data.id);
    });

    // Listen for highlight removal
    this.highlighter.on(EventType.REMOVE, (data, inst, e) => {
      console.log("🗑️ Highlight removed:", data);
      // Clean up stored metadata
      if (data.ids) {
        data.ids.forEach((id) => this.highlightStore.delete(id));
      }
    });
  }

  /**
   * Handle highlight creation and implement selective replacement logic
   * This handles overlapping highlights by splitting existing ones and creating new ones
   */
  handleHighlightCreate(data) {
    const currentColor = this.getCurrentColor();
    if (!currentColor) {
      console.warn("⚠️ No current color available");
      return;
    }

    const newHighlight = data.sources[0];

    // Skip processing if this is a split highlight being restored
    if (data.type === "from-store" || newHighlight.id.startsWith("split-")) {
      console.log(`⏭️ Skipping split highlight processing: ${newHighlight.id}`);
      return;
    }

    console.log(
      `🎯 Processing new highlight: (${newHighlight.startMeta.textOffset}-${newHighlight.endMeta.textOffset}) ${currentColor.id}`
    );

    // Find overlapping highlights in our stored data
    const overlappingHighlights =
      this.findOverlappingStoredHighlights(newHighlight);

    if (overlappingHighlights.length > 0) {
      console.log(
        `🔍 Found ${overlappingHighlights.length} overlapping highlights`
      );

      // Process each overlapping highlight
      overlappingHighlights.forEach((existingHighlight) => {
        this.handleOverlappingHighlight(
          existingHighlight,
          newHighlight,
          currentColor
        );
      });
    }

    // Store the new highlight data (NO TEXT STORAGE for efficiency)
    const highlightData = {
      startMeta: newHighlight.startMeta,
      endMeta: newHighlight.endMeta,
      id: newHighlight.id,
      colorId: currentColor.id,
    };

    this.highlightStore.set(newHighlight.id, highlightData);

    // Apply color styling using web-highlighter's built-in addClass
    this.applyColorStyling(newHighlight.id, currentColor.id);

    // Notify parent component
    if (this.onSelectionCreate) {
      this.onSelectionCreate({
        sources: [highlightData],
      });
    }

    console.log("✨ New highlight created and stored:", highlightData.id);
  }

  /**
   * Find overlapping highlights in our stored data (not DOM-based)
   */
  findOverlappingStoredHighlights(newHighlight) {
    const overlapping = [];

    for (const [id, existingHighlight] of this.highlightStore) {
      if (this.highlightsOverlap(existingHighlight, newHighlight)) {
        overlapping.push(existingHighlight);
      }
    }

    return overlapping;
  }

  /**
   * Check if two highlights overlap based on their text offsets
   */
  highlightsOverlap(highlight1, highlight2) {
    // Check if they're in the same parent element
    if (
      highlight1.startMeta.parentTagName !==
        highlight2.startMeta.parentTagName ||
      highlight1.startMeta.parentIndex !== highlight2.startMeta.parentIndex
    ) {
      return false;
    }

    const start1 = highlight1.startMeta.textOffset;
    const end1 = highlight1.endMeta.textOffset;
    const start2 = highlight2.startMeta.textOffset;
    const end2 = highlight2.endMeta.textOffset;

    // Check for overlap: start1 < end2 && start2 < end1
    return start1 < end2 && start2 < end1;
  }

  /**
   * Handle overlapping highlight by splitting the existing one
   */
  handleOverlappingHighlight(existingHighlight, newHighlight, newColor) {
    console.log(`🔧 Splitting overlapping highlight: ${existingHighlight.id}`);

    const existingStart = existingHighlight.startMeta.textOffset;
    const existingEnd = existingHighlight.endMeta.textOffset;
    const newStart = newHighlight.startMeta.textOffset;
    const newEnd = newHighlight.endMeta.textOffset;

    // Remove the existing highlight from DOM and storage
    this.highlighter.remove(existingHighlight.id);
    this.highlightStore.delete(existingHighlight.id);

    // Create before portion if it exists (keep original color)
    if (existingStart < newStart) {
      const beforeHighlight = {
        startMeta: existingHighlight.startMeta,
        endMeta: {
          ...existingHighlight.endMeta,
          textOffset: newStart,
        },
        id: this.generateUniqueId(),
        colorId: existingHighlight.colorId, // Keep original color
      };

      this.createSplitHighlight(beforeHighlight);
      console.log(`📝 Created before portion (${existingHighlight.colorId})`);
    }

    // Create after portion if it exists (keep original color)
    if (newEnd < existingEnd) {
      const afterHighlight = {
        startMeta: {
          ...existingHighlight.startMeta,
          textOffset: newEnd,
        },
        endMeta: existingHighlight.endMeta,
        id: this.generateUniqueId(),
        colorId: existingHighlight.colorId, // Keep original color
      };

      this.createSplitHighlight(afterHighlight);
      console.log(`📝 Created after portion (${existingHighlight.colorId})`);
    }
  }

  /**
   * Create a split highlight portion (NO TEXT STORAGE)
   */
  createSplitHighlight(highlightData) {
    // Store the split highlight (no text for efficiency)
    this.highlightStore.set(highlightData.id, highlightData);

    // Create the highlight using web-highlighter's fromStore
    // According to docs: fromStore(startMeta, endMeta, text, id)
    // We need to extract text from DOM for web-highlighter, but don't store it
    const extractedText = this.extractTextFromRange(
      highlightData.startMeta,
      highlightData.endMeta
    );

    this.highlighter.fromStore(
      highlightData.startMeta,
      highlightData.endMeta,
      extractedText, // Only used by web-highlighter, not stored
      highlightData.id
    );

    // Apply the original color
    this.applyColorStyling(highlightData.id, highlightData.colorId);

    // Notify parent component directly (bypass event system)
    if (this.onSelectionCreate) {
      this.onSelectionCreate({
        sources: [highlightData],
      });
    }
  }

  /**
   * Extract text from DOM range for web-highlighter (not stored)
   */
  extractTextFromRange(startMeta, endMeta) {
    try {
      // Find the parent element
      const parentElements = this.container.getElementsByTagName(
        startMeta.parentTagName
      );
      if (parentElements.length <= startMeta.parentIndex) {
        return "";
      }

      const parentElement = parentElements[startMeta.parentIndex];
      const fullText = parentElement.textContent || "";

      // Extract the text portion based on offsets
      return fullText.substring(startMeta.textOffset, endMeta.textOffset);
    } catch (error) {
      console.warn("⚠️ Failed to extract text from range:", error);
      return "";
    }
  }

  /**
   * Generate a unique ID for split highlights
   */
  generateUniqueId() {
    return (
      "split-" + Math.random().toString(36).substr(2, 9) + "-" + Date.now()
    );
  }

  /**
   * Apply color styling using web-highlighter's built-in addClass method
   */
  applyColorStyling(highlightId, colorId) {
    const colorClass = `highlight-${colorId}`;
    this.highlighter.addClass(colorClass, highlightId);
    console.log(`🎨 Applied ${colorId} styling to highlight ${highlightId}`);
  }

  /**
   * Start the highlighter with floating button support
   */
  run() {
    this.isRunning = true;

    // Use web-highlighter's built-in run() method for automatic highlighting
    this.highlighter.run();

    // Add selection change listener for floating button
    this.setupSelectionListener();

    console.log(
      "🚀 Web-Highlighter is now running with floating button support"
    );
  }

  /**
   * Stop the highlighter
   */
  stop() {
    this.isRunning = false;

    // Use web-highlighter's built-in stop() method
    this.highlighter.stop();

    // Remove selection listener and floating button
    this.removeSelectionListener();
    this.hideFloatingButton();

    console.log("⏹️ Web-Highlighter stopped");
  }

  /**
   * Setup selection change listener for floating button
   */
  setupSelectionListener() {
    // Bind methods to preserve 'this' context
    this.boundSelectionChange = this.handleSelectionChange.bind(this);
    this.boundMouseUp = this.handleMouseUp.bind(this);
    this.boundDocumentClick = this.handleDocumentClick.bind(this);

    // Listen for selection changes
    document.addEventListener("selectionchange", this.boundSelectionChange);
    document.addEventListener("mouseup", this.boundMouseUp);
    document.addEventListener("click", this.boundDocumentClick);
  }

  /**
   * Remove selection listener
   */
  removeSelectionListener() {
    if (this.boundSelectionChange) {
      document.removeEventListener(
        "selectionchange",
        this.boundSelectionChange
      );
    }
    if (this.boundMouseUp) {
      document.removeEventListener("mouseup", this.boundMouseUp);
    }
    if (this.boundDocumentClick) {
      document.removeEventListener("click", this.boundDocumentClick);
    }
  }

  /**
   * Handle selection changes to show/hide floating button
   */
  handleSelectionChange() {
    const selection = window.getSelection();

    if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
      this.hideFloatingButton();
      this.currentSelection = null;
      return;
    }

    const range = selection.getRangeAt(0);

    // Check if selection is within our container
    if (!this.container.contains(range.commonAncestorContainer)) {
      this.hideFloatingButton();
      this.currentSelection = null;
      return;
    }

    // Check if the selected text contains highlighted content
    if (this.selectionContainsHighlights(range)) {
      this.currentSelection = range.cloneRange();
      // Temporarily stop web-highlighter to prevent automatic highlighting
      this.highlighter.stop();
      this.showFloatingButton(range);
    } else {
      this.hideFloatingButton();
      this.currentSelection = null;
      // Ensure web-highlighter is running for normal highlighting
      if (this.isRunning) {
        this.highlighter.run();
      }
    }
  }

  /**
   * Handle mouse up to position floating button correctly
   */
  handleMouseUp(event) {
    // Small delay to ensure selection is finalized and floating button can be clicked
    setTimeout(() => {
      if (this.currentSelection) {
        this.positionFloatingButton(this.currentSelection);
      }
    }, 50); // Increased delay to allow button interaction
  }

  /**
   * Handle document clicks to hide floating button when clicking outside
   */
  handleDocumentClick(event) {
    // Don't hide if clicking on the floating button itself
    if (this.floatingButton && this.floatingButton.contains(event.target)) {
      return;
    }

    // Don't hide immediately after showing the button
    if (this.floatingButton && this.floatingButton.style.display === "block") {
      // Small delay to allow button interaction
      setTimeout(() => {
        // Check if we still have a selection with highlights
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
          this.hideFloatingButton();
          this.currentSelection = null;
        }
      }, 100);
    }
  }

  /**
   * Check if selection contains highlighted content
   */
  selectionContainsHighlights(range) {
    const walker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          if (node.classList && node.classList.contains("web-highlight-wrap")) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        },
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      if (range.intersectsNode(node)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Show floating button for unhighlighting
   */
  showFloatingButton(range) {
    if (!this.floatingButton) {
      this.createFloatingButton();
    }

    this.floatingButton.style.display = "block";
    this.positionFloatingButton(range);
  }

  /**
   * Hide floating button and restart web-highlighter
   */
  hideFloatingButton() {
    if (this.floatingButton) {
      this.floatingButton.style.display = "none";
    }

    // Restart web-highlighter for normal highlighting when button is hidden
    if (this.isRunning) {
      this.highlighter.run();
    }
  }

  /**
   * Create floating button element
   */
  createFloatingButton() {
    this.floatingButton = document.createElement("div");
    this.floatingButton.className = "web-highlighter-floating-button";
    this.floatingButton.innerHTML = `
      <button class="unhighlight-btn" title="Remove highlight">
        🗑️ Unhighlight
      </button>
    `;

    // Add styles
    this.floatingButton.style.cssText = `
      position: absolute;
      z-index: 10000;
      background: white;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      padding: 4px;
      display: none;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    const button = this.floatingButton.querySelector(".unhighlight-btn");
    button.style.cssText = `
      background: #ff4757;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
    `;

    // Add hover effect
    button.addEventListener("mouseenter", () => {
      button.style.background = "#ff3742";
    });

    button.addEventListener("mouseleave", () => {
      button.style.background = "#ff4757";
    });

    // Add click handler for unhighlighting
    button.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.handleUnhighlight();
    });

    // Add to document
    document.body.appendChild(this.floatingButton);
  }

  /**
   * Position floating button near the selection
   */
  positionFloatingButton(range) {
    if (!this.floatingButton) return;

    const rect = range.getBoundingClientRect();
    const buttonHeight = 40; // Approximate button height
    const buttonWidth = 120; // Approximate button width

    let top = rect.top - buttonHeight - 5; // Position above selection
    let left = rect.left + rect.width / 2 - buttonWidth / 2; // Center horizontally

    // Adjust if button would be outside viewport
    if (top < 0) {
      top = rect.bottom + 5; // Position below if no space above
    }

    if (left < 0) {
      left = 5;
    } else if (left + buttonWidth > window.innerWidth) {
      left = window.innerWidth - buttonWidth - 5;
    }

    // Add scroll offset
    top += window.scrollY;
    left += window.scrollX;

    this.floatingButton.style.top = `${top}px`;
    this.floatingButton.style.left = `${left}px`;
  }

  /**
   * Handle unhighlight action - selective unhighlighting
   */
  handleUnhighlight() {
    if (!this.currentSelection) return;

    console.log("🗑️ Starting selective unhighlighting");

    try {
      // Find highlights that overlap with the selection
      const overlappingHighlights = this.findHighlightsInRange(
        this.currentSelection
      );

      if (overlappingHighlights.length === 0) {
        console.log("⚠️ No highlights found in selection");
        this.hideFloatingButton();
        return;
      }

      // Process each overlapping highlight for selective removal
      overlappingHighlights.forEach((highlightId) => {
        this.selectivelyRemoveHighlight(highlightId, this.currentSelection);
      });

      // Clear selection and hide button
      window.getSelection().removeAllRanges();
      this.hideFloatingButton();
      this.currentSelection = null;

      // Restart web-highlighter for normal highlighting
      if (this.isRunning) {
        this.highlighter.run();
      }

      console.log("✅ Selective unhighlighting completed");
    } catch (error) {
      console.error("❌ Error during selective unhighlighting:", error);
    }
  }

  /**
   * Find highlight IDs that intersect with the given range
   */
  findHighlightsInRange(range) {
    const highlightIds = [];

    // Get all highlight elements in the range
    const walker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          if (node.classList && node.classList.contains("web-highlight-wrap")) {
            return NodeFilter.FILTER_ACCEPT;
          }
          return NodeFilter.FILTER_SKIP;
        },
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      if (range.intersectsNode(node)) {
        // Get highlight ID from the element
        const highlightId = this.getHighlightIdFromElement(node);
        if (highlightId && !highlightIds.includes(highlightId)) {
          highlightIds.push(highlightId);
        }
      }
    }

    return highlightIds;
  }

  /**
   * Get highlight ID from DOM element
   */
  getHighlightIdFromElement(element) {
    // Web-highlighter typically stores ID in data attributes or class names
    // Check for data-highlight-id or extract from class names
    if (element.dataset && element.dataset.highlightId) {
      return element.dataset.highlightId;
    }

    // Try to get ID using web-highlighter's built-in method if available
    try {
      return this.highlighter.getIdByDom(element);
    } catch (error) {
      console.warn("⚠️ Could not get highlight ID from element:", error);
      return null;
    }
  }

  /**
   * Selectively remove highlight portion that overlaps with selection
   */
  selectivelyRemoveHighlight(highlightId, selectionRange) {
    console.log(`🔧 Selectively removing highlight: ${highlightId}`);

    try {
      // Get the highlight data
      const highlightData = this.highlightStore.get(highlightId);
      if (!highlightData) {
        console.warn(`⚠️ No stored data found for highlight: ${highlightId}`);
        return;
      }

      // Remove the entire highlight first
      this.highlighter.remove(highlightId);
      this.highlightStore.delete(highlightId);

      // Calculate what portions should be preserved (before and after selection)
      const preservedPortions = this.calculatePreservedPortions(
        highlightData,
        selectionRange
      );

      // Recreate the preserved portions as new highlights
      preservedPortions.forEach((portion) => {
        this.createSplitHighlight(portion);
      });

      console.log(`✅ Selectively removed highlight: ${highlightId}`);
    } catch (error) {
      console.error(
        `❌ Error selectively removing highlight ${highlightId}:`,
        error
      );
    }
  }

  /**
   * Calculate preserved portions when selectively removing a highlight
   */
  calculatePreservedPortions(highlightData, selectionRange) {
    const preservedPortions = [];

    try {
      // Get selection boundaries
      const selectionStart = this.getOffsetInParent(
        selectionRange.startContainer,
        selectionRange.startOffset
      );
      const selectionEnd = this.getOffsetInParent(
        selectionRange.endContainer,
        selectionRange.endOffset
      );

      // Get highlight boundaries
      const highlightStart = highlightData.startMeta.textOffset;
      const highlightEnd = highlightData.endMeta.textOffset;

      // Create before portion if it exists
      if (highlightStart < selectionStart) {
        const beforePortion = {
          startMeta: highlightData.startMeta,
          endMeta: {
            ...highlightData.endMeta,
            textOffset: selectionStart,
          },
          id: this.generateUniqueId(),
          colorId: highlightData.colorId,
        };
        preservedPortions.push(beforePortion);
      }

      // Create after portion if it exists
      if (selectionEnd < highlightEnd) {
        const afterPortion = {
          startMeta: {
            ...highlightData.startMeta,
            textOffset: selectionEnd,
          },
          endMeta: highlightData.endMeta,
          id: this.generateUniqueId(),
          colorId: highlightData.colorId,
        };
        preservedPortions.push(afterPortion);
      }
    } catch (error) {
      console.warn("⚠️ Error calculating preserved portions:", error);
    }

    return preservedPortions;
  }

  /**
   * Get text offset within parent element
   */
  getOffsetInParent(node, offset) {
    let textOffset = offset;
    let currentNode = node;

    // If node is an element, find the text offset
    while (currentNode && currentNode !== this.container) {
      const parent = currentNode.parentNode;
      if (!parent) break;

      // Add text length of all previous siblings
      let sibling = parent.firstChild;
      while (sibling && sibling !== currentNode) {
        if (sibling.nodeType === Node.TEXT_NODE) {
          textOffset += sibling.textContent.length;
        } else if (sibling.nodeType === Node.ELEMENT_NODE) {
          textOffset += sibling.textContent.length;
        }
        sibling = sibling.nextSibling;
      }

      currentNode = parent;
    }

    return textOffset;
  }

  /**
   * Remove a highlight by ID using web-highlighter's built-in remove
   */
  removeHighlight(highlightId) {
    try {
      this.highlighter.remove(highlightId);
      this.highlightStore.delete(highlightId);
      console.log(`🗑️ Removed highlight: ${highlightId}`);
      return true;
    } catch (error) {
      console.error("❌ Failed to remove highlight:", error);
      return false;
    }
  }

  /**
   * Remove all highlights using web-highlighter's built-in removeAll
   */
  removeAll() {
    try {
      this.highlighter.removeAll();
      this.highlightStore.clear();
      console.log("🧹 All highlights removed");
      return true;
    } catch (error) {
      console.error("❌ Failed to remove all highlights:", error);
      return false;
    }
  }

  /**
   * Get all highlights using web-highlighter's built-in getAll
   */
  getAllHighlights() {
    try {
      return this.highlighter.getAll();
    } catch (error) {
      console.error("❌ Failed to get all highlights:", error);
      return [];
    }
  }

  /**
   * Serialize highlights for storage - return array of highlight objects
   * Each highlight should have: startMeta, endMeta, text, id
   */
  serialize() {
    try {
      // Get all highlights and convert them to the format expected by fromStore
      const allHighlights = [];

      // Get all highlight IDs (if this method exists)
      // For now, we'll rely on the CREATE event to store highlights
      // and return the stored highlights from our internal store

      const highlights = Array.from(this.highlightStore.values());
      console.log(`📦 Serializing ${highlights.length} highlights`);

      return highlights;
    } catch (error) {
      console.error("❌ Failed to serialize highlights:", error);
      return [];
    }
  }

  /**
   * Deserialize and restore highlights using web-highlighter's built-in fromStore
   * According to documentation: fromStore(startMeta, endMeta, text, id)
   * Handles deduplication to prevent overlapping highlight issues
   */
  deserialize(highlights) {
    try {
      if (!highlights || !Array.isArray(highlights)) {
        console.log("📭 No highlights to restore");
        return true;
      }

      // Deduplicate highlights by ID, keeping only the latest version
      const deduplicatedHighlights = this.deduplicateHighlights(highlights);

      console.log(
        `🔄 Restoring ${deduplicatedHighlights.length} unique highlights (from ${highlights.length} total)...`
      );

      deduplicatedHighlights.forEach((highlight, index) => {
        try {
          // Extract text from DOM for web-highlighter (not stored for efficiency)
          const extractedText = this.extractTextFromRange(
            highlight.startMeta,
            highlight.endMeta
          );

          // Use web-highlighter's fromStore method with individual parameters
          // According to docs: fromStore(startMeta, endMeta, text, id)
          this.highlighter.fromStore(
            highlight.startMeta,
            highlight.endMeta,
            extractedText, // Extracted from DOM, not stored
            highlight.id
          );

          // Apply the correct color styling
          this.applyColorStyling(highlight.id, highlight.colorId);

          console.log(
            `✅ Restored highlight ${index + 1}/${
              deduplicatedHighlights.length
            } (${highlight.colorId})`
          );
        } catch (error) {
          console.warn(`⚠️ Failed to restore highlight ${index + 1}:`, error);
        }
      });

      console.log("✅ All highlights restoration completed");
      return true;
    } catch (error) {
      console.error("❌ Failed to deserialize highlights:", error);
      return false;
    }
  }

  /**
   * Deduplicate highlights by ID, keeping the latest version of each highlight
   */
  deduplicateHighlights(highlights) {
    const highlightMap = new Map();

    // Process highlights in order, later ones will overwrite earlier ones
    highlights.forEach((highlight) => {
      const existingHighlight = highlightMap.get(highlight.id);

      // Keep the highlight with the latest createdAt timestamp
      if (
        !existingHighlight ||
        (highlight.createdAt &&
          existingHighlight.createdAt &&
          new Date(highlight.createdAt) > new Date(existingHighlight.createdAt))
      ) {
        highlightMap.set(highlight.id, highlight);
      }
    });

    const deduplicated = Array.from(highlightMap.values());
    console.log(
      `🔧 Deduplicated ${highlights.length} highlights to ${deduplicated.length} unique highlights`
    );

    return deduplicated;
  }
}

/**
 * Create highlighter instance using web-highlighter
 */
export const createHighlighter = async (
  container,
  onHighlightClick,
  onSelectionCreate,
  getCurrentColor
) => {
  try {
    console.log("🚀 Creating Web-Highlighter wrapper...");

    const wrapper = new WebHighlighterWrapper(
      container,
      onHighlightClick,
      onSelectionCreate,
      getCurrentColor
    );

    const initialized = await wrapper.initialize();
    if (!initialized) {
      throw new Error("Failed to initialize Web-Highlighter wrapper");
    }

    console.log("✅ Web-Highlighter wrapper created successfully");
    return wrapper;
  } catch (error) {
    console.error("❌ Failed to create Web-Highlighter wrapper:", error);
    return null;
  }
};

export { HighlightSource, WebHighlighterWrapper };
