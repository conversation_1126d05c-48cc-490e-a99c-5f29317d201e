/**
 * Test script for Rangy Highlight Replacement functionality
 * This script can be run in the browser console to test the highlighting system
 */

// Test configuration
const TEST_CONFIG = {
  colors: {
    yellow: { id: "yellow", name: "Yellow", color: "#ffeb3b", backgroundColor: "rgba(255, 235, 59, 0.3)" },
    green: { id: "green", name: "<PERSON>", color: "#8bc34a", backgroundColor: "rgba(139, 195, 74, 0.3)" },
    blue: { id: "blue", name: "Blue", color: "#90caf9", backgroundColor: "rgba(144, 202, 249, 0.3)" }
  },
  testText: "it is the study of bodies in motion"
};

/**
 * Test Suite for Highlight Replacement
 */
class HighlightReplacementTest {
  constructor() {
    this.results = [];
    this.container = null;
    this.highlighter = null;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(`%c${logMessage}`, 
      type === 'success' ? 'color: green; font-weight: bold' :
      type === 'error' ? 'color: red; font-weight: bold' :
      type === 'warning' ? 'color: orange; font-weight: bold' :
      'color: blue'
    );
    
    this.results.push({ timestamp, message, type });
  }

  /**
   * Initialize test environment
   */
  async initialize() {
    try {
      this.log('🚀 Initializing Highlight Replacement Test Suite');
      
      // Create test container
      this.container = document.createElement('div');
      this.container.id = 'test-container';
      this.container.innerHTML = `
        <p>Physics is a fundamental science that seeks to understand how the universe works. 
        ${TEST_CONFIG.testText}, it is divided into two parts: classical mechanics 
        and quantum mechanics.</p>
      `;
      this.container.style.cssText = `
        position: fixed;
        top: 50px;
        left: 50px;
        width: 500px;
        padding: 20px;
        background: white;
        border: 2px solid #ccc;
        border-radius: 8px;
        font-family: Arial, sans-serif;
        line-height: 1.6;
        z-index: 10000;
      `;
      
      document.body.appendChild(this.container);
      
      // Add styles for highlights
      this.addHighlightStyles();
      
      this.log('✅ Test environment initialized', 'success');
      return true;
      
    } catch (error) {
      this.log(`❌ Failed to initialize test environment: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Add CSS styles for highlights
   */
  addHighlightStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .rangy-highlight-yellow {
        background-color: rgba(255, 235, 59, 0.3) !important;
        border-bottom: 2px solid #ffeb3b !important;
      }
      .rangy-highlight-green {
        background-color: rgba(139, 195, 74, 0.3) !important;
        border-bottom: 2px solid #8bc34a !important;
      }
      .rangy-highlight-blue {
        background-color: rgba(144, 202, 249, 0.3) !important;
        border-bottom: 2px solid #90caf9 !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Test 1: Basic highlight creation
   */
  async testBasicHighlight() {
    this.log('🧪 Test 1: Basic highlight creation');
    
    try {
      // Find the test text
      const textNode = this.findTextNode(TEST_CONFIG.testText);
      if (!textNode) {
        throw new Error('Test text not found');
      }

      // Create range for the full text
      const range = document.createRange();
      const startOffset = textNode.textContent.indexOf(TEST_CONFIG.testText);
      range.setStart(textNode, startOffset);
      range.setEnd(textNode, startOffset + TEST_CONFIG.testText.length);

      // Apply yellow highlight
      this.applyHighlight(range, TEST_CONFIG.colors.yellow);
      
      // Verify highlight was created
      const highlights = this.container.querySelectorAll('.rangy-highlight-yellow');
      if (highlights.length > 0) {
        this.log('✅ Basic highlight created successfully', 'success');
        return true;
      } else {
        throw new Error('No highlight elements found');
      }
      
    } catch (error) {
      this.log(`❌ Test 1 failed: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Test 2: Highlight replacement
   */
  async testHighlightReplacement() {
    this.log('🧪 Test 2: Highlight replacement');
    
    try {
      // Find the word "study" within the highlighted text
      const highlightElement = this.container.querySelector('.rangy-highlight-yellow');
      if (!highlightElement) {
        throw new Error('No yellow highlight found for replacement test');
      }

      const studyText = "study";
      const fullText = highlightElement.textContent;
      const studyIndex = fullText.indexOf(studyText);
      
      if (studyIndex === -1) {
        throw new Error('Word "study" not found in highlighted text');
      }

      // Create range for just "study"
      const range = document.createRange();
      const textNode = highlightElement.firstChild;
      range.setStart(textNode, studyIndex);
      range.setEnd(textNode, studyIndex + studyText.length);

      // Apply green highlight (this should trigger replacement)
      this.applyHighlight(range, TEST_CONFIG.colors.green);
      
      // Verify replacement worked
      const yellowHighlights = this.container.querySelectorAll('.rangy-highlight-yellow');
      const greenHighlights = this.container.querySelectorAll('.rangy-highlight-green');
      
      if (greenHighlights.length > 0 && yellowHighlights.length >= 1) {
        this.log('✅ Highlight replacement successful', 'success');
        this.log(`   Found ${yellowHighlights.length} yellow highlights and ${greenHighlights.length} green highlights`);
        return true;
      } else {
        throw new Error(`Expected yellow and green highlights, found ${yellowHighlights.length} yellow, ${greenHighlights.length} green`);
      }
      
    } catch (error) {
      this.log(`❌ Test 2 failed: ${error.message}`, 'error');
      return false;
    }
  }

  /**
   * Apply highlight to a range (simplified version)
   */
  applyHighlight(range, color) {
    const span = document.createElement('span');
    span.className = `rangy-highlight-${color.id}`;
    span.style.backgroundColor = color.backgroundColor;
    span.style.borderBottom = `2px solid ${color.color}`;
    
    try {
      range.surroundContents(span);
    } catch (error) {
      // If surroundContents fails, extract and wrap content
      const contents = range.extractContents();
      span.appendChild(contents);
      range.insertNode(span);
    }
  }

  /**
   * Find text node containing specific text
   */
  findTextNode(searchText) {
    const walker = document.createTreeWalker(
      this.container,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let node;
    while (node = walker.nextNode()) {
      if (node.textContent.includes(searchText)) {
        return node;
      }
    }
    return null;
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    this.log('🎯 Starting Highlight Replacement Test Suite');
    
    const initialized = await this.initialize();
    if (!initialized) {
      this.log('❌ Test suite initialization failed', 'error');
      return;
    }

    const tests = [
      this.testBasicHighlight.bind(this),
      this.testHighlightReplacement.bind(this)
    ];

    let passed = 0;
    let failed = 0;

    for (let i = 0; i < tests.length; i++) {
      const result = await tests[i]();
      if (result) {
        passed++;
      } else {
        failed++;
      }
      
      // Add delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.log(`📊 Test Results: ${passed} passed, ${failed} failed`, 
      failed === 0 ? 'success' : 'warning');
    
    // Keep test container visible for inspection
    this.log('🔍 Test container will remain visible for inspection');
    
    return { passed, failed, results: this.results };
  }

  /**
   * Clean up test environment
   */
  cleanup() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
    this.log('🧹 Test environment cleaned up');
  }
}

// Export for use
if (typeof window !== 'undefined') {
  window.HighlightReplacementTest = HighlightReplacementTest;
  
  // Auto-run if script is loaded directly
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🚀 Highlight Replacement Test Suite loaded. Run: new HighlightReplacementTest().runAllTests()');
    });
  } else {
    console.log('🚀 Highlight Replacement Test Suite loaded. Run: new HighlightReplacementTest().runAllTests()');
  }
}

// Usage example:
// const test = new HighlightReplacementTest();
// test.runAllTests().then(results => console.log('Tests completed:', results));
