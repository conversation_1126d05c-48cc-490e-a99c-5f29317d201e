<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rangy Highlight Replacement Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        line-height: 1.6;
      }

      .content {
        background: #f9f9f9;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        font-size: 18px;
      }

      .controls {
        margin: 20px 0;
        padding: 15px;
        background: #e9e9e9;
        border-radius: 5px;
      }

      .color-button {
        padding: 8px 16px;
        margin: 5px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
      }

      .color-button.yellow {
        background: rgba(255, 235, 59, 0.7);
      }
      .color-button.green {
        background: rgba(139, 195, 74, 0.7);
      }
      .color-button.blue {
        background: rgba(144, 202, 249, 0.7);
      }
      .color-button.pink {
        background: rgba(244, 143, 177, 0.7);
      }
      .color-button.purple {
        background: rgba(206, 147, 216, 0.7);
      }

      .color-button.active {
        border: 3px solid #333;
      }

      /* Rangy highlight styles */
      .rangy-highlight-yellow {
        background-color: rgba(255, 235, 59, 0.3) !important;
        border-bottom: 2px solid #ffeb3b !important;
        cursor: pointer !important;
      }

      .rangy-highlight-green {
        background-color: rgba(139, 195, 74, 0.3) !important;
        border-bottom: 2px solid #8bc34a !important;
        cursor: pointer !important;
      }

      .rangy-highlight-blue {
        background-color: rgba(144, 202, 249, 0.3) !important;
        border-bottom: 2px solid #90caf9 !important;
        cursor: pointer !important;
      }

      .rangy-highlight-pink {
        background-color: rgba(244, 143, 177, 0.3) !important;
        border-bottom: 2px solid #f48fb1 !important;
        cursor: pointer !important;
      }

      .rangy-highlight-purple {
        background-color: rgba(206, 147, 216, 0.3) !important;
        border-bottom: 2px solid #ce93d8 !important;
        cursor: pointer !important;
      }

      .instructions {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
      }

      .log {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        padding: 10px;
        border-radius: 4px;
        max-height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <h1>Rangy Highlight Replacement Test</h1>

    <div class="instructions">
      <h3>Test Instructions:</h3>
      <ol>
        <li>
          First, select "it is the study of bodies in motion" and highlight it
          with <strong>Yellow</strong>
        </li>
        <li>
          Then, select just the word "study" and highlight it with
          <strong>Green</strong>
        </li>
        <li>
          The result should be: "it is the" (yellow) + "study" (green) + "of
          bodies in motion" (yellow)
        </li>
        <li>
          Try different combinations to test the replacement functionality
        </li>
      </ol>
    </div>

    <div class="controls">
      <strong>Select Color:</strong>
      <button class="color-button yellow active" data-color="yellow">
        Yellow
      </button>
      <button class="color-button green" data-color="green">Green</button>
      <button class="color-button blue" data-color="blue">Blue</button>
      <button class="color-button pink" data-color="pink">Pink</button>
      <button class="color-button purple" data-color="purple">Purple</button>
      <button
        onclick="clearAllHighlights()"
        style="
          margin-left: 20px;
          padding: 8px 16px;
          background: #dc3545;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        "
      >
        Clear All
      </button>
    </div>

    <div class="content" id="content">
      <p>
        Physics is a fundamental science that seeks to understand how the
        universe works. It is the study of bodies in motion, it is divided into
        two parts: classical mechanics and quantum mechanics. Classical
        mechanics deals with the motion of objects that are large enough to be
        seen with the naked eye, while quantum mechanics deals with the motion
        of very small particles like atoms and electrons.
      </p>

      <p>
        The study of physics has led to many important discoveries and
        inventions that have changed the world. From the laws of motion
        discovered by Newton to the theory of relativity by Einstein, physics
        continues to push the boundaries of human knowledge.
      </p>
    </div>

    <div class="log" id="log">
      <div>Console log will appear here...</div>
    </div>

    <!-- Load Rangy -->
    <script src="https://cdn.jsdelivr.net/npm/rangy@1.3.2/lib/rangy-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rangy@1.3.2/lib/rangy-classapplier.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rangy@1.3.2/lib/rangy-highlighter.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/rangy@1.3.2/lib/rangy-serializer.min.js"></script>

    <script>
      // Initialize variables
      let highlighter = null;
      let currentColor = "yellow";

      // Color definitions
      const COLORS = {
        yellow: {
          id: "yellow",
          name: "Yellow",
          color: "#ffeb3b",
          backgroundColor: "rgba(255, 235, 59, 0.3)",
        },
        green: {
          id: "green",
          name: "Green",
          color: "#8bc34a",
          backgroundColor: "rgba(139, 195, 74, 0.3)",
        },
        blue: {
          id: "blue",
          name: "Blue",
          color: "#90caf9",
          backgroundColor: "rgba(144, 202, 249, 0.3)",
        },
        pink: {
          id: "pink",
          name: "Pink",
          color: "#f48fb1",
          backgroundColor: "rgba(244, 143, 177, 0.3)",
        },
        purple: {
          id: "purple",
          name: "Purple",
          color: "#ce93d8",
          backgroundColor: "rgba(206, 147, 216, 0.3)",
        },
      };

      // Logging function
      function log(message) {
        const logDiv = document.getElementById("log");
        const timestamp = new Date().toLocaleTimeString();
        logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(message);
      }

      // Initialize Rangy
      function initializeRangy() {
        try {
          rangy.init();
          highlighter = rangy.createHighlighter();

          // Create class appliers for each color
          Object.values(COLORS).forEach((color) => {
            const className = `rangy-highlight-${color.id}`;
            const applier = rangy.createClassApplier(className, {
              elementTagName: "span",
              normalize: true,
              useExistingElements: false,
            });
            highlighter.addClassApplier(applier);
          });

          log("✅ Rangy initialized successfully");
          return true;
        } catch (error) {
          log("❌ Failed to initialize Rangy: " + error.message);
          return false;
        }
      }

      // Handle text selection
      function handleSelection() {
        const selection = window.getSelection();
        if (!selection || selection.rangeCount === 0) return;

        const range = selection.getRangeAt(0);
        if (range.collapsed) return;

        const contentDiv = document.getElementById("content");
        if (!contentDiv.contains(range.commonAncestorContainer)) return;

        log(
          `🎯 Creating ${
            COLORS[currentColor].name
          } highlight for: "${range.toString()}"`
        );

        try {
          // Apply replacement highlighting
          replaceHighlightSelection(range, COLORS[currentColor]);
          selection.removeAllRanges();
        } catch (error) {
          log("❌ Error creating highlight: " + error.message);
        }
      }

      // Replacement highlighting function
      function replaceHighlightSelection(range, color) {
        const contentDiv = document.getElementById("content");

        // Find overlapping highlights
        const overlappingElements = findOverlappingHighlights(
          range,
          contentDiv
        );
        log(`🔍 Found ${overlappingElements.length} overlapping highlights`);

        // Remove overlapping highlights and get preserved ranges
        const preservedRanges = removeOverlappingHighlights(
          overlappingElements,
          range
        );

        // Apply new highlight
        const className = `rangy-highlight-${color.id}`;
        const tempSelection = rangy.getSelection();
        tempSelection.removeAllRanges();
        tempSelection.addRange(range);
        highlighter.highlightSelection(className);

        // Restore preserved ranges
        restorePreservedRanges(preservedRanges);

        log(`✅ Applied ${color.name} highlight with replacement`);
      }

      // Find overlapping highlight elements
      function findOverlappingHighlights(range, container) {
        const overlapping = [];
        const highlightElements = container.querySelectorAll(
          '[class*="rangy-highlight-"]'
        );

        highlightElements.forEach((element) => {
          try {
            const elementRange = document.createRange();
            elementRange.selectNodeContents(element);

            if (rangesOverlap(range, elementRange)) {
              const colorId = extractColorFromClassName(element.className);
              overlapping.push({
                element: element,
                range: elementRange,
                colorId: colorId,
                text: element.textContent,
              });
            }
          } catch (error) {
            log("⚠️ Error checking overlap: " + error.message);
          }
        });

        return overlapping;
      }

      // Check if ranges overlap
      function rangesOverlap(range1, range2) {
        try {
          const comparison1 = range1.compareBoundaryPoints(
            Range.START_TO_END,
            range2
          );
          const comparison2 = range1.compareBoundaryPoints(
            Range.END_TO_START,
            range2
          );
          return comparison1 > 0 && comparison2 < 0;
        } catch (error) {
          return false;
        }
      }

      // Extract color from class name
      function extractColorFromClassName(className) {
        const match = className.match(/rangy-highlight-(\w+)/);
        return match ? match[1] : null;
      }

      // Remove overlapping highlights and return preserved ranges
      function removeOverlappingHighlights(overlappingElements, newRange) {
        const preservedRanges = [];

        overlappingElements.forEach((highlight) => {
          try {
            const element = highlight.element;
            const elementRange = highlight.range;
            const colorId = highlight.colorId;

            log(
              `🔍 Processing overlapping ${colorId} highlight: "${element.textContent}"`
            );
            log(`🔍 New selection: "${newRange.toString()}"`);

            // Calculate preserved ranges using proper range intersection
            const beforeRange = getRangeBeforeOverlap(
              elementRange,
              newRange,
              element
            );
            const afterRange = getRangeAfterOverlap(
              elementRange,
              newRange,
              element
            );

            // Store ranges to preserve with their color
            if (beforeRange && beforeRange.text && beforeRange.text.trim()) {
              preservedRanges.push({
                text: beforeRange.text,
                colorId: colorId,
                position: "before",
                referenceElement: element,
              });
              log(`📝 Will preserve BEFORE: "${beforeRange.text}"`);
            }

            if (afterRange && afterRange.text && afterRange.text.trim()) {
              preservedRanges.push({
                text: afterRange.text,
                colorId: colorId,
                position: "after",
                referenceElement: element,
              });
              log(`📝 Will preserve AFTER: "${afterRange.text}"`);
            }

            // Remove the original element
            const parent = element.parentNode;
            const textNode = document.createTextNode(element.textContent);
            parent.replaceChild(textNode, element);
            parent.normalize();

            log(`🗑️ Removed overlapping ${colorId} highlight`);
          } catch (error) {
            log("⚠️ Error removing highlight: " + error.message);
          }
        });

        return preservedRanges;
      }

      // Get the range before the overlap (improved text-based approach)
      function getRangeBeforeOverlap(originalRange, newRange, element) {
        try {
          const fullText = element.textContent;
          const newText = newRange.toString().trim();

          // Find the exact position of the new selection within the highlighted text
          let startIndex = -1;

          // Try to find the new text within the full text
          const tempIndex = fullText.indexOf(newText);
          if (tempIndex !== -1) {
            startIndex = tempIndex;
          } else {
            // If exact match not found, try to find it by comparing with selection
            // This handles cases where there might be slight differences in whitespace
            const words = newText.split(/\s+/);
            if (words.length > 0) {
              const firstWord = words[0];
              const lastWord = words[words.length - 1];
              const firstWordIndex = fullText.indexOf(firstWord);
              const lastWordIndex = fullText.lastIndexOf(lastWord);

              if (firstWordIndex !== -1 && lastWordIndex !== -1) {
                startIndex = firstWordIndex;
              }
            }
          }

          if (startIndex > 0) {
            const beforeText = fullText.substring(0, startIndex).trim();
            if (beforeText) {
              log(`📍 Found BEFORE text: "${beforeText}" (from position 0 to ${startIndex})`);
              return { text: beforeText };
            }
          }

          return null;
        } catch (error) {
          log("⚠️ Error creating before range: " + error.message);
          return null;
        }
      }

      // Get the range after the overlap (improved text-based approach)
      function getRangeAfterOverlap(originalRange, newRange, element) {
        try {
          const fullText = element.textContent;
          const newText = newRange.toString().trim();

          // Find the exact position of the new selection within the highlighted text
          let startIndex = -1;
          let endIndex = -1;

          // Try to find the new text within the full text
          const tempIndex = fullText.indexOf(newText);
          if (tempIndex !== -1) {
            startIndex = tempIndex;
            endIndex = startIndex + newText.length;
          } else {
            // If exact match not found, try to find it by comparing with selection
            const words = newText.split(/\s+/);
            if (words.length > 0) {
              const firstWord = words[0];
              const lastWord = words[words.length - 1];
              const firstWordIndex = fullText.indexOf(firstWord);
              const lastWordIndex = fullText.lastIndexOf(lastWord);

              if (firstWordIndex !== -1 && lastWordIndex !== -1) {
                startIndex = firstWordIndex;
                endIndex = lastWordIndex + lastWord.length;
              }
            }
          }

          if (startIndex !== -1 && endIndex !== -1 && endIndex < fullText.length) {
            const afterText = fullText.substring(endIndex).trim();
            if (afterText) {
              log(`📍 Found AFTER text: "${afterText}" (from position ${endIndex} to end)`);
              return { text: afterText };
            }
          }

          return null;
        } catch (error) {
          log("⚠️ Error creating after range: " + error.message);
          return null;
        }
      }

      // Restore preserved text ranges with their colors
      function restorePreservedRanges(preservedRanges) {
        // Sort ranges by position to maintain proper order
        const sortedRanges = preservedRanges.sort((a, b) => {
          if (a.position === "before" && b.position === "after") return -1;
          if (a.position === "after" && b.position === "before") return 1;
          return 0;
        });

        sortedRanges.forEach((preserved) => {
          try {
            const color = COLORS[preserved.colorId];
            if (color && preserved.text.trim()) {
              log(`🔧 Restoring ${color.name} highlight for: "${preserved.text}"`);

              // Find the text in the document to create a proper range
              const textToFind = preserved.text;
              const contentDiv = document.getElementById("content");

              // Use a more sophisticated approach to find and highlight the text
              const walker = document.createTreeWalker(
                contentDiv,
                NodeFilter.SHOW_TEXT,
                null,
                false
              );

              let found = false;
              let node;
              while ((node = walker.nextNode()) && !found) {
                const nodeText = node.textContent;
                const index = nodeText.indexOf(textToFind);

                if (index !== -1) {
                  // Create range for the found text
                  const range = document.createRange();
                  range.setStart(node, index);
                  range.setEnd(node, index + textToFind.length);

                  // Apply highlight using rangy
                  const className = `rangy-highlight-${color.id}`;
                  const tempSelection = rangy.getSelection();
                  tempSelection.removeAllRanges();
                  tempSelection.addRange(range);
                  highlighter.highlightSelection(className);

                  found = true;
                  log(`✅ Restored ${color.name} highlight for: "${preserved.text}"`);
                }
              }

              if (!found) {
                log(`⚠️ Could not find text to restore: "${preserved.text}"`);
              }
            }
          } catch (error) {
            log("⚠️ Error restoring range: " + error.message);
          }
        });

              log(
                `✅ Restored ${color.name} highlight for: "${preserved.text}"`
              );
            }
          } catch (error) {
            log("⚠️ Error restoring range: " + error.message);
          }
        });
      }

      // Color selection handlers
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize Rangy
        if (!initializeRangy()) {
          log("❌ Failed to initialize highlighting system");
          return;
        }

        // Color button handlers
        document.querySelectorAll(".color-button").forEach((button) => {
          button.addEventListener("click", function () {
            // Remove active class from all buttons
            document
              .querySelectorAll(".color-button")
              .forEach((b) => b.classList.remove("active"));

            // Add active class to clicked button
            this.classList.add("active");

            // Set current color
            currentColor = this.dataset.color;
            log(`🎨 Selected color: ${COLORS[currentColor].name}`);
          });
        });

        // Selection handler
        document.addEventListener("mouseup", handleSelection);

        log("🚀 Highlight replacement system ready!");
      });

      // Clear all highlights
      function clearAllHighlights() {
        try {
          highlighter.removeAllHighlights();
          log("🧹 All highlights cleared");
        } catch (error) {
          log("❌ Error clearing highlights: " + error.message);
        }
      }
    </script>
  </body>
</html>
