<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web-Highlighter Replacement Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .content {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 18px;
        }
        
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #e9e9e9;
            border-radius: 5px;
        }
        
        .color-button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .color-button.yellow { background: rgba(255, 235, 59, 0.7); }
        .color-button.green { background: rgba(139, 195, 74, 0.7); }
        .color-button.blue { background: rgba(144, 202, 249, 0.7); }
        .color-button.pink { background: rgba(244, 143, 177, 0.7); }
        .color-button.purple { background: rgba(206, 147, 216, 0.7); }
        
        .color-button.active {
            border: 3px solid #333;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }

        /* Web-Highlighter Styles */
        .web-highlight-wrap {
            cursor: pointer !important;
            transition: all 0.2s ease;
        }

        .web-highlight-wrap:hover {
            opacity: 0.8;
        }

        /* Color-specific highlight styles */
        .highlight-yellow {
            background-color: rgba(255, 235, 59, 0.3) !important;
            border-bottom: 2px solid #ffeb3b !important;
        }

        .highlight-green {
            background-color: rgba(139, 195, 74, 0.3) !important;
            border-bottom: 2px solid #8bc34a !important;
        }

        .highlight-blue {
            background-color: rgba(144, 202, 249, 0.3) !important;
            border-bottom: 2px solid #90caf9 !important;
        }

        .highlight-pink {
            background-color: rgba(244, 143, 177, 0.3) !important;
            border-bottom: 2px solid #f48fb1 !important;
        }

        .highlight-purple {
            background-color: rgba(206, 147, 216, 0.3) !important;
            border-bottom: 2px solid #ce93d8 !important;
        }

        /* Hover effect for highlights */
        .highlight-hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
            transform: translateY(-1px) !important;
        }
    </style>
</head>
<body>
    <h1>Web-Highlighter Replacement Test</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>First, select "Physics is a fundamental science that seeks to understand" and highlight it with <strong>Yellow</strong></li>
            <li>Then, select just the word "understand" and highlight it with <strong>Green</strong></li>
            <li>The result should be: "Physics is a fundamental science that seeks to" (yellow) + "understand" (green)</li>
            <li>Try different combinations to test the replacement functionality</li>
        </ol>
    </div>
    
    <div class="controls">
        <strong>Select Color:</strong>
        <button class="color-button yellow active" data-color="yellow">Yellow</button>
        <button class="color-button green" data-color="green">Green</button>
        <button class="color-button blue" data-color="blue">Blue</button>
        <button class="color-button pink" data-color="pink">Pink</button>
        <button class="color-button purple" data-color="purple">Purple</button>
        <button onclick="clearAllHighlights()" style="margin-left: 20px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">Clear All</button>
    </div>
    
    <div class="content" id="content">
        <p>Physics is a fundamental science that seeks to understand how the universe works. It is the study of bodies in motion, it is divided into two parts: classical mechanics and quantum mechanics. Classical mechanics deals with the motion of objects that are large enough to be seen with the naked eye, while quantum mechanics deals with the motion of very small particles like atoms and electrons.</p>
        
        <p>The study of physics has led to many important discoveries and inventions that have changed the world. From the laws of motion discovered by Newton to the theory of relativity by Einstein, physics continues to push the boundaries of human knowledge.</p>
    </div>
    
    <div class="log" id="log">
        <div>Console log will appear here...</div>
    </div>

    <!-- Load Web-Highlighter -->
    <script src="https://cdn.jsdelivr.net/npm/web-highlighter@0.7.3/dist/web-highlighter.min.js"></script>

    <script>
        // Initialize variables
        let highlighter = null;
        let currentColor = 'yellow';
        
        // Color definitions
        const COLORS = {
            yellow: { id: "yellow", name: "Yellow", color: "#ffeb3b", backgroundColor: "rgba(255, 235, 59, 0.3)" },
            green: { id: "green", name: "Green", color: "#8bc34a", backgroundColor: "rgba(139, 195, 74, 0.3)" },
            blue: { id: "blue", name: "Blue", color: "#90caf9", backgroundColor: "rgba(144, 202, 249, 0.3)" },
            pink: { id: "pink", name: "Pink", color: "#f48fb1", backgroundColor: "rgba(244, 143, 177, 0.3)" },
            purple: { id: "purple", name: "Purple", color: "#ce93d8", backgroundColor: "rgba(206, 147, 216, 0.3)" }
        };
        
        // Logging function
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Initialize Web-Highlighter
        function initializeWebHighlighter() {
            try {
                const contentDiv = document.getElementById('content');
                
                // Create web-highlighter instance
                highlighter = new window.Highlighter({
                    $root: contentDiv,
                    exceptSelectors: ['pre', 'code', 'script', 'style'],
                    wrapTag: 'span',
                    verbose: true,
                    style: {
                        className: 'web-highlight-wrap'
                    }
                });

                // Set up event listeners
                highlighter.on(window.Highlighter.event.CREATE, (data, inst, e) => {
                    log(`🎯 Highlight created: ${data.sources.length} source(s)`);
                    
                    // Apply color styling to the newly created highlight
                    if (data.sources.length > 0) {
                        const highlightId = data.sources[0].id;
                        const colorClass = `highlight-${currentColor}`;
                        highlighter.addClass(colorClass, highlightId);
                        log(`🎨 Applied ${COLORS[currentColor].name} styling`);
                    }
                });

                highlighter.on(window.Highlighter.event.CLICK, (data, inst, e) => {
                    log(`🖱️ Highlight clicked: ${data.id}`);
                });

                highlighter.on(window.Highlighter.event.HOVER, (data, inst, e) => {
                    highlighter.addClass('highlight-hover', data.id);
                });

                highlighter.on(window.Highlighter.event.HOVER_OUT, (data, inst, e) => {
                    highlighter.removeClass('highlight-hover', data.id);
                });

                highlighter.on(window.Highlighter.event.REMOVE, (data, inst, e) => {
                    log(`🗑️ Highlight removed: ${data.ids ? data.ids.length : 1} highlight(s)`);
                });
                
                log("✅ Web-Highlighter initialized successfully");
                return true;
            } catch (error) {
                log("❌ Failed to initialize Web-Highlighter: " + error.message);
                return false;
            }
        }
        
        // Handle text selection with replacement logic
        function handleSelection() {
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0) return;
            
            const range = selection.getRangeAt(0);
            if (range.collapsed) return;
            
            const contentDiv = document.getElementById('content');
            if (!contentDiv.contains(range.commonAncestorContainer)) return;
            
            log(`🎯 Creating ${COLORS[currentColor].name} highlight for: "${range.toString()}"`);
            
            try {
                // Use web-highlighter's built-in fromRange method
                highlighter.fromRange(range);
                selection.removeAllRanges();
            } catch (error) {
                log("❌ Error creating highlight: " + error.message);
            }
        }
        
        // Color selection handlers
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Web-Highlighter
            if (!initializeWebHighlighter()) {
                log("❌ Failed to initialize highlighting system");
                return;
            }
            
            // Color button handlers
            document.querySelectorAll('.color-button').forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    document.querySelectorAll('.color-button').forEach(b => b.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    // Set current color
                    currentColor = this.dataset.color;
                    log(`🎨 Selected color: ${COLORS[currentColor].name}`);
                });
            });
            
            // Selection handler
            document.addEventListener('mouseup', handleSelection);
            
            log("🚀 Web-Highlighter system ready!");
        });
        
        // Clear all highlights
        function clearAllHighlights() {
            try {
                highlighter.removeAll();
                log("🧹 All highlights cleared");
            } catch (error) {
                log("❌ Error clearing highlights: " + error.message);
            }
        }
    </script>
</body>
</html>
